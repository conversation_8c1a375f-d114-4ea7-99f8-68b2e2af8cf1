{"welfare_secondtest_finish":false,"autoshow_loginreward":false,"equipfuben_max_pass_floor":0,"equipfuben_cachefloor_1":1,"autoshow_welfare_rewardback":false,"audio_autoplay_channel":{},"common_channel":[1,2,3,4,103],"confirmtiptime":{"firstchargeeff":1754032763},"equipfuben_cachefloor":1,"chapter_fuben_lockpartner":true,"welfare_secondtest":true,"last_login_time":1754034566}