{"autoshow_loginreward":false,"autoshow_welfare_rewardback":false,"audio_autoplay_channel":{},"last_login_time":1754034254,"equipfuben_max_pass_floor":0,"common_channel":[1,2,3,4,103],"confirmtiptime":{"firstchargeeff":1754032763},"equipfuben_cachefloor":1,"welfare_secondtest_finish":false,"welfare_secondtest":true,"chapter_fuben_lockpartner":true,"equipfuben_cachefloor_1":1}