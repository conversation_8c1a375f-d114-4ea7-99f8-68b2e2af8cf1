with:1287  hight:491
[logic/login/CLoginIcnTiShi.lua:2]:!!!!!!!!!!!!!!!!!CLoginIcnTiShi!!!!!!!!!!!!!!!!!
谷歌支付初始化失败
谷歌支付初始化失败
[logic/misc/CShareCtrl.lua:42]:jit    true    SSE3    SSE4.1    BMI2    fold    cse    dce    fwd    dse    narrow    loop    abc    sink    fuse
[logic/login/CLoginCtrl.lua:5]:公告初始化...
[logic/base/CHttpCtrl.lua:32]:http get ->    https://openapi.baidu.com/oauth/2.0/token?client_id=rlU84dprH6I7sMjBkqM0gizl&grant_type=client_credentials&client_secret=c15395c2433c4a904d22aa27eab8321b&
[logic/ui/CViewCtrl.lua:94]:CNotifyView ShowView
[logic/ui/CViewCtrl.lua:94]:CBottomView ShowView
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x60efa790"
|  json_result = true
|  timer = 16
}
[logic/audio/CSpeechCtrl.lua:72]:OnTokenGet err
[logic/ui/CViewBase.lua:125]:CNotifyView LoadDone!
[logic/ui/CViewBase.lua:125]:CBottomView LoadDone!
[logic/base/CResCtrl.lua:199]:-->resource init done!!! 12
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {}
[logic/ui/CViewCtrl.lua:94]:CLoginView ShowView
[logic/login/CLoginIcnTiShi.lua:4]:~~~~~~~~~~~~CLoginIcnTiShi.ctor~~~~~~~~~~~~~~~~~~
[logic/base/CHttpCtrl.lua:32]:http get ->    http://************:88/Note/note2408.json
[logic/ui/CViewBase.lua:125]:CLoginView LoadDone!
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x6cb01fc0"
|  json_result = true
|  timer = 61
}
[core/table.lua:94]:cb tResult-> = {
|  info = {
|  |  role_list = {}
|  |  server_info = {
|  |  |  groups = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 1
|  |  |  |  |  name = "1-10区"
|  |  |  |  }
|  |  |  }
|  |  |  infoList = {
|  |  |  |  [1] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]服务器在8月16日22点00分进行不停机维护，解决ios无法使用可选御灵道具，0点掉线问题！[-]"
|  |  |  |  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "维护公告"
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]欢迎来到《剑与火之歌（0.05折）》！[-]"
|  |  |  |  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "开服了"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]单日累充活动[-]"
|  |  |  |  |  |  |  |  title = "【活动类型】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*120[-]"
|  |  |  |  |  |  |  |  title = "单日充值1500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [11] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  |  |  |  title = "单日充值2500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [12] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*5、万能碎片*500、原石盒子*20、附魔材料盒子*20[-]"
|  |  |  |  |  |  |  |  title = "单日充值5000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [13] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.每日均可参与，高档位可领所有低档位；
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  |  |  |  title = "【活动时间】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]单日真实充值达到指定金额，即可领取该档位的奖励，本活动需要对应真实折后充值参与（高档位可以领取低档位的奖励)，每日活动重置[-]"
|  |  |  |  |  |  |  |  title = "【活动说明】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  |  |  |  title = "【领取方式】"
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "[ff0000]武器原石*5、星梦矿*10、金币*20000[-]"
|  |  |  |  |  |  |  |  title = "单日充值50元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*10、体力药水*1、鲜肉包*30、金币*100000[-]"
|  |  |  |  |  |  |  |  title = "单日充值150元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂碎片*20、体力药水*2、焦糖包*60、金币*200000[-]"
|  |  |  |  |  |  |  |  title = "单日充值250元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  |  |  |  title = "单日充值500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  |  |  |  title = "单日充值1000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "单日累充"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]永久累计充值活动[-]"
|  |  |  |  |  |  |  |  title = "活动名称："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [10] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*400、一发入魂碎片*100、原石盒子*40、附魔材料盒子*40、金币*3000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值7000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [11] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*700、一发入魂碎片*200、原石盒子*50、附魔材料盒子*50、金币*5000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值10000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [12] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.各档位奖励永久仅可获得一次返利，高档位可领所有低档位。
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额，活动解释权归游戏方所有。[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  |  |  |  title = "领取方式："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [3] = {
|  |  |  |  |  |  |  |  text = "[ff0000]活动为永久累计真实充值，高档位可领取低档位所有物品（每个档位奖励仅可领取一次）[-]"
|  |  |  |  |  |  |  |  title = "活动说明："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [4] = {
|  |  |  |  |  |  |  |  text = "[ff0000]梦觉书·零*50、星梦矿*50、金币*10000[-]"
|  |  |  |  |  |  |  |  title = "累计充值300元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [5] = {
|  |  |  |  |  |  |  |  text = "[ff0000]2星精英伙伴*10、原石盒子*5、附魔材料盒子*5、金币*50000[-]"
|  |  |  |  |  |  |  |  title = "累计充值500元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [6] = {
|  |  |  |  |  |  |  |  text = "[ff0000]2星精英伙伴*20、原石盒子*10、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  |  |  |  title = "累计充值1000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [7] = {
|  |  |  |  |  |  |  |  text = "[ff0000]一发入魂契约*1、原石盒子*30、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  |  |  |  title = "累计充值2000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [8] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*100、一发入魂碎片*100、原石盒子*20、附魔材料盒子*20、金币*1000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值3000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [9] = {
|  |  |  |  |  |  |  |  text = "[ff0000]万能碎片*200、一发入魂碎片*200、原石盒子*30、附魔材料盒子*30、金币*2000000[-]"
|  |  |  |  |  |  |  |  title = "累计充值5000元："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "累计充值"
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  content = {
|  |  |  |  |  |  contents = {
|  |  |  |  |  |  |  [1] = {
|  |  |  |  |  |  |  |  text = "[ff0000]所有区服开服之日起3天内，第一个累计实际真实充值金额1000元的玩家，可获得该区服务器冠名权+3星传说伙伴任选*1（需提供具体道具名，伙伴包括：亦候、袁雀、白殊、莹月、奉主夜鹤、浮屠僧）[-]"
|  |  |  |  |  |  |  |  title = "服务器冠名活动："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  |  [2] = {
|  |  |  |  |  |  |  |  text = "[ff0000]1.达成条件的玩家需向客服申请，核实后将于5个工作日内完成冠名及发放奖励至玩家邮箱；
2.资格获得者可以自定义自己所在区服的服务器名称，冠名后不可修改，请慎重取名；
3.更改后，该区服名称永久有效；
4.服务器命名规则：不超过4个汉字，不支持符号、英文，禁止色情反动、诋毁他人等不文明或破坏社会风气的词语。
后面满足条件的玩家无法获得冠名权限但新服前三名满足条件的玩家都可以联系客服领取到冠名奖励呦！

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  |  |  |  title = "备注："
|  |  |  |  |  |  |  }
|  |  |  |  |  |  }
|  |  |  |  |  |  pic = ""
|  |  |  |  |  |  text = ""
|  |  |  |  |  }
|  |  |  |  |  hot = 2
|  |  |  |  |  title = "冠名活动"
|  |  |  |  }
|  |  |  }
|  |  |  ports = {}
|  |  |  role_list = ""
|  |  |  servers = {
|  |  |  |  [1] = {
|  |  |  |  |  group = 1
|  |  |  |  |  id = 1
|  |  |  |  |  ip = "************"
|  |  |  |  |  name = "剑与火之歌"
|  |  |  |  |  new = 1
|  |  |  |  |  open_time = 1711501140
|  |  |  |  |  ports = {
|  |  |  |  |  |  [1] = 7011
|  |  |  |  |  }
|  |  |  |  |  server_id = "bus_gs10001"
|  |  |  |  |  start_time = 1711501140
|  |  |  |  |  state = 1
|  |  |  |  }
|  |  |  }
|  |  }
|  |  token = ""
|  }
}
[core/global.lua:59]:<color=#ffeb04>CServerCtrl.SetServerData - 2</color>
[core/table.lua:94]:CServerCtrl.SetServerData: = {
|  [1] = {
|  |  group_id = 1
|  |  id = 1
|  |  name = "1-10区"
|  |  servers = {
|  |  |  [1] = {
|  |  |  |  group = 1
|  |  |  |  id = 1
|  |  |  |  ip = "************"
|  |  |  |  name = "剑与火之歌"
|  |  |  |  new = 1
|  |  |  |  now_server_id = "bus_gs10001"
|  |  |  |  open_time = 1711501140
|  |  |  |  ports = {
|  |  |  |  |  [1] = 7011
|  |  |  |  }
|  |  |  |  server_id = 1
|  |  |  |  start_time = 1711501140
|  |  |  |  state = 1
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:设置公告: = {
|  [1] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]服务器在8月16日22点00分进行不停机维护，解决ios无法使用可选御灵道具，0点掉线问题！[-]"
|  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "维护公告"
|  }
|  [2] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]欢迎来到《剑与火之歌（0.05折）》！[-]"
|  |  |  |  |  title = "亲爱的玩家们："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "开服了"
|  }
|  [3] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]单日累充活动[-]"
|  |  |  |  |  title = "【活动类型】"
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*1、万能碎片*100、50级橙武礼包*1、皮肤券*120[-]"
|  |  |  |  |  title = "单日充值1500元："
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*2、万能碎片*200、60级橙装礼包*1、9级宝石礼包*1[-]"
|  |  |  |  |  title = "单日充值2500元："
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*5、万能碎片*500、原石盒子*20、附魔材料盒子*20[-]"
|  |  |  |  |  title = "单日充值5000元："
|  |  |  |  }
|  |  |  |  [13] = {
|  |  |  |  |  text = "[ff0000]1.每日均可参与，高档位可领所有低档位；
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]永久[-]"
|  |  |  |  |  title = "【活动时间】"
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]单日真实充值达到指定金额，即可领取该档位的奖励，本活动需要对应真实折后充值参与（高档位可以领取低档位的奖励)，每日活动重置[-]"
|  |  |  |  |  title = "【活动说明】"
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  title = "【领取方式】"
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "[ff0000]武器原石*5、星梦矿*10、金币*20000[-]"
|  |  |  |  |  title = "单日充值50元："
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*10、体力药水*1、鲜肉包*30、金币*100000[-]"
|  |  |  |  |  title = "单日充值150元："
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "[ff0000]一发入魂碎片*20、体力药水*2、焦糖包*60、金币*200000[-]"
|  |  |  |  |  title = "单日充值250元："
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*1、附魔材料礼包*1、星梦矿礼盒*1、淬灵云晶礼袋*1[-]"
|  |  |  |  |  title = "单日充值500元："
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "[ff0000]装备原石礼包*3、附魔材料礼包*3、星梦矿礼盒*3、淬灵云晶礼袋*3[-]"
|  |  |  |  |  title = "单日充值1000元："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "单日累充"
|  }
|  [4] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]永久累计充值活动[-]"
|  |  |  |  |  title = "活动名称："
|  |  |  |  }
|  |  |  |  [10] = {
|  |  |  |  |  text = "[ff0000]万能碎片*400、一发入魂碎片*100、原石盒子*40、附魔材料盒子*40、金币*3000000[-]"
|  |  |  |  |  title = "累计充值7000元："
|  |  |  |  }
|  |  |  |  [11] = {
|  |  |  |  |  text = "[ff0000]万能碎片*700、一发入魂碎片*200、原石盒子*50、附魔材料盒子*50、金币*5000000[-]"
|  |  |  |  |  title = "累计充值10000元："
|  |  |  |  }
|  |  |  |  [12] = {
|  |  |  |  |  text = "[ff0000]1.各档位奖励永久仅可获得一次返利，高档位可领所有低档位。
2.请联系平台客服申请返利，返利以礼包码形式发放。

注：上述实际金额为折扣后真实付费金额，活动解释权归游戏方所有。[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]联系客服申请领取或咨询申请邮件发放[-]"
|  |  |  |  |  title = "领取方式："
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  text = "[ff0000]活动为永久累计真实充值，高档位可领取低档位所有物品（每个档位奖励仅可领取一次）[-]"
|  |  |  |  |  title = "活动说明："
|  |  |  |  }
|  |  |  |  [4] = {
|  |  |  |  |  text = "[ff0000]梦觉书·零*50、星梦矿*50、金币*10000[-]"
|  |  |  |  |  title = "累计充值300元："
|  |  |  |  }
|  |  |  |  [5] = {
|  |  |  |  |  text = "[ff0000]2星精英伙伴*10、原石盒子*5、附魔材料盒子*5、金币*50000[-]"
|  |  |  |  |  title = "累计充值500元："
|  |  |  |  }
|  |  |  |  [6] = {
|  |  |  |  |  text = "[ff0000]2星精英伙伴*20、原石盒子*10、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  title = "累计充值1000元："
|  |  |  |  }
|  |  |  |  [7] = {
|  |  |  |  |  text = "[ff0000]一发入魂契约*1、原石盒子*30、附魔材料盒子*10、金币*100000[-]"
|  |  |  |  |  title = "累计充值2000元："
|  |  |  |  }
|  |  |  |  [8] = {
|  |  |  |  |  text = "[ff0000]万能碎片*100、一发入魂碎片*100、原石盒子*20、附魔材料盒子*20、金币*1000000[-]"
|  |  |  |  |  title = "累计充值3000元："
|  |  |  |  }
|  |  |  |  [9] = {
|  |  |  |  |  text = "[ff0000]万能碎片*200、一发入魂碎片*200、原石盒子*30、附魔材料盒子*30、金币*2000000[-]"
|  |  |  |  |  title = "累计充值5000元："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "累计充值"
|  }
|  [5] = {
|  |  content = {
|  |  |  contents = {
|  |  |  |  [1] = {
|  |  |  |  |  text = "[ff0000]所有区服开服之日起3天内，第一个累计实际真实充值金额1000元的玩家，可获得该区服务器冠名权+3星传说伙伴任选*1（需提供具体道具名，伙伴包括：亦候、袁雀、白殊、莹月、奉主夜鹤、浮屠僧）[-]"
|  |  |  |  |  title = "服务器冠名活动："
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  text = "[ff0000]1.达成条件的玩家需向客服申请，核实后将于5个工作日内完成冠名及发放奖励至玩家邮箱；
2.资格获得者可以自定义自己所在区服的服务器名称，冠名后不可修改，请慎重取名；
3.更改后，该区服名称永久有效；
4.服务器命名规则：不超过4个汉字，不支持符号、英文，禁止色情反动、诋毁他人等不文明或破坏社会风气的词语。
后面满足条件的玩家无法获得冠名权限但新服前三名满足条件的玩家都可以联系客服领取到冠名奖励呦！

注：上述实际金额为折扣后真实付费金额。[-]"
|  |  |  |  |  title = "备注："
|  |  |  |  }
|  |  |  }
|  |  |  pic = ""
|  |  |  text = ""
|  |  }
|  |  hot = 2
|  |  title = "冠名活动"
|  }
}
[core/table.lua:94]:http post -> = {
|  headers = {
|  |  Content-Type = "application/x-www-form-urlencoded"
|  }
|  url = "http://************:8081/common/accountList"
}
[core/global.lua:59]:<color=#ffeb04>CLoginServerPage.SetServer</color>
[logic/login/CLoginCtrl.lua:154]:ConnectServer =     table:0x6CB083D8    table:0x6C998130
[net/CNetCtrl.lua:114]:Test连接    ************    7011
[core/table.lua:94]:-->Net Receive: login.GS2CHello = {
|  time = **********
}
[core/table.lua:94]:<--Net Send: login.C2GSQueryLogin = {
|  res_file_version = {}
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:49:26
[net/netlogin.lua:210]:netlogin.GS2CQueryLogin-->
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 1001
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginAccount = {
|  account = "w8773610226"
|  client_svn_version = 96
|  client_version = "1.15.0.0"
|  device = "82RC (LENOVO)"
|  imei = ""
|  is_qrcode = 0
|  mac = "9C-2D-CD-1C-C7-E5"
|  os = "Windows 10  (10.0.0) 64bit"
|  platform = 3
|  udid = "d1bcbaffb9feae17994f24d0a8ec9d4d08cb3f95"
}
[core/global.lua:59]:<color=#ffeb04>更新代码: 1049</color>
[logic/base/Utils.lua:402]:local framever, dllver, resver = C_api.Utils.GetResVersion()
if resver >= 96 or Utils.IsEditor() then
	function CWelfareCtrl.IsOpenCostSave(self)
		local b = false
		local t = g_TimeCtrl:GetTimeS()	
		if self.m_CostSaveStartTime ~= 0 and self.m_CostSaveEndTime ~= 0 and t > self.m_CostSaveStartTime and t < self.m_CostSaveEndTime then
			b = true
		end
		return b
	end
	function CPartnerHireView.IsCanYFRH(self, iParID)
		local d = data.partnerhiredata.DATA
		if table.index(d[iParID]["recommand_list"], "一发入魂") then
			return self:IsCanSSRDraw()
		end
	end
	function CPartnerEquipPage.ChangePart(self)
		local oItem = g_ItemCtrl:GetItem(self.m_CurItemID)
		if oItem and oItem:GetValue("level") == define.Partner.ParEquip.MaxLevel and oItem:GetValue("star") < define.Partner.ParEquip.MaxStar then
			if self.m_CurPart ~= self.m_UpStarPart and self.m_CurPart ~= self.m_UpStonePart then
				self.m_UpStarBtn:SetSelected(true)
				self:SwitchPart()
			end
		end
	end
	data.netdata.BAN["proto"]["herobox"]["item"]["GS2CItemQuickUse"] = true
end

[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 1001
}
[core/table.lua:94]:-->Net Receive: login.GS2CCheckInviteCodeResult = {
|  msg = "邀请码验证已关闭"
|  result = 1
}
[core/table.lua:94]:-->Net Receive: login.GS2CLoginAccount = {
|  account = "w8773610226"
|  role_list = {
|  |  [1] = {
|  |  |  grade = 13
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  }
|  |  |  |  shape = 120
|  |  |  |  weapon = 2500
|  |  |  }
|  |  |  pid = 10008
|  |  }
|  }
}
[core/table.lua:94]:<--Net Send: login.C2GSLoginRole = {
|  account = "w8773610226"
|  pid = 10008
}
[core/global.lua:59]:<color=#ffeb04>读取系统设置本地数据，帐号为  w8773610226</color>
[core/global.lua:59]:<color=#ffeb04>w8773610226 的系统设置本地数据为 nil，使用默认数据</color>
[core/table.lua:94]:-->Net Receive: login.GS2CLoginRole = {
|  account = "w8773610226"
|  pid = 10008
|  role = {
|  |  abnormal_attr_ratio = 550
|  |  active = 7
|  |  attack = 324
|  |  bcmd = {
|  |  |  [1] = {
|  |  |  |  idx = 208
|  |  |  }
|  |  |  [10] = {
|  |  |  |  idx = 105
|  |  |  }
|  |  |  [11] = {
|  |  |  |  idx = 106
|  |  |  }
|  |  |  [12] = {
|  |  |  |  cmd = "减速"
|  |  |  |  idx = 203
|  |  |  }
|  |  |  [13] = {
|  |  |  |  idx = 108
|  |  |  }
|  |  |  [14] = {
|  |  |  |  idx = 205
|  |  |  }
|  |  |  [15] = {
|  |  |  |  idx = 206
|  |  |  }
|  |  |  [16] = {
|  |  |  |  idx = 207
|  |  |  }
|  |  |  [2] = {
|  |  |  |  idx = 107
|  |  |  }
|  |  |  [3] = {
|  |  |  |  cmd = "禁疗"
|  |  |  |  idx = 204
|  |  |  }
|  |  |  [4] = {
|  |  |  |  cmd = "集火"
|  |  |  |  idx = 201
|  |  |  }
|  |  |  [5] = {
|  |  |  |  cmd = "封印"
|  |  |  |  idx = 202
|  |  |  }
|  |  |  [6] = {
|  |  |  |  cmd = "治疗"
|  |  |  |  idx = 101
|  |  |  }
|  |  |  [7] = {
|  |  |  |  cmd = "复活"
|  |  |  |  idx = 102
|  |  |  }
|  |  |  [8] = {
|  |  |  |  cmd = "加速"
|  |  |  |  idx = 103
|  |  |  }
|  |  |  [9] = {
|  |  |  |  cmd = "解封"
|  |  |  |  idx = 104
|  |  |  }
|  |  }
|  |  coin = 792750
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  energy = 243
|  |  exp = 19960
|  |  goldcoin = 5090
|  |  grade = 13
|  |  kp_sdk_info = {
|  |  |  create_time = 1751861123
|  |  |  upgrade_time = 1753782121
|  |  }
|  |  mask = "7e6683d1ffffffe"
|  |  max_hp = 3091
|  |  model_info = {
|  |  |  color = {
|  |  |  |  [1] = 0
|  |  |  }
|  |  |  shape = 120
|  |  |  weapon = 2500
|  |  }
|  |  name = "颔首之孵化者"
|  |  open_day = 65
|  |  org_fuben_cnt = 2
|  |  power = 963
|  |  res_abnormal_ratio = 550
|  |  res_critical_ratio = 500
|  |  school = 1
|  |  school_branch = 1
|  |  sex = 2
|  |  show_id = 10008
|  |  skill_point = 12
|  |  speed = 753
|  |  systemsetting = {}
|  }
|  role_token = "**************"
|  xg_account = "bus10008"
}
[logic/ui/CViewCtrl.lua:94]:CMainMenuView ShowView
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 550
|  active = 7
|  arenamedal = 0
|  attack = 324
|  bcmd = {
|  |  [1] = {
|  |  |  idx = 208
|  |  }
|  |  [10] = {
|  |  |  idx = 105
|  |  }
|  |  [11] = {
|  |  |  idx = 106
|  |  }
|  |  [12] = {
|  |  |  cmd = "减速"
|  |  |  idx = 203
|  |  }
|  |  [13] = {
|  |  |  idx = 108
|  |  }
|  |  [14] = {
|  |  |  idx = 205
|  |  }
|  |  [15] = {
|  |  |  idx = 206
|  |  }
|  |  [16] = {
|  |  |  idx = 207
|  |  }
|  |  [2] = {
|  |  |  idx = 107
|  |  }
|  |  [3] = {
|  |  |  cmd = "禁疗"
|  |  |  idx = 204
|  |  }
|  |  [4] = {
|  |  |  cmd = "集火"
|  |  |  idx = 201
|  |  }
|  |  [5] = {
|  |  |  cmd = "封印"
|  |  |  idx = 202
|  |  }
|  |  [6] = {
|  |  |  cmd = "治疗"
|  |  |  idx = 101
|  |  }
|  |  [7] = {
|  |  |  cmd = "复活"
|  |  |  idx = 102
|  |  }
|  |  [8] = {
|  |  |  cmd = "加速"
|  |  |  idx = 103
|  |  }
|  |  [9] = {
|  |  |  cmd = "解封"
|  |  |  idx = 104
|  |  }
|  }
|  camp = 0
|  chatself = false
|  chubeiexp = 0
|  coin = 792750
|  coin_over = 0
|  color_coin = 0
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  energy = 243
|  exp = 19960
|  followers = {}
|  goldcoin = 5090
|  grade = 13
|  hp = 0
|  kp_sdk_info = {
|  |  create_time = 1751861123
|  |  upgrade_time = 1753782121
|  }
|  max_hp = 3091
|  medal = 0
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  }
|  |  shape = 120
|  |  weapon = 2500
|  }
|  name = "颔首之孵化者"
|  open_day = 65
|  org_fuben_cnt = 2
|  org_offer = 0
|  power = 963
|  res_abnormal_ratio = 550
|  res_critical_ratio = 500
|  school = 1
|  school_branch = 1
|  sex = 2
|  show_id = 10008
|  skill_point = 12
|  skin = 0
|  speed = 753
|  systemsetting = {}
|  title_info = {}
|  trapmine_point = 0
|  travel_score = 0
|  upvote_amount = 0
}
[logic/misc/CTimeCtrl.lua:64]:HeartBeat-->开启心跳包检测
[core/table.lua:94]:<--Net Send: title.C2GSTitleInfoList = {}
[core/table.lua:94]:-->Net Receive: player.GS2CGamePushSetting = {}
[core/table.lua:94]:-->Net Receive: player.GS2CShapeList = {
|  shapes = {
|  |  [1] = 120
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CGuidanceInfo = {
|  guidanceinfo = {
|  |  key = {
|  |  |  [1] = 7000041
|  |  |  [10] = 3002099
|  |  |  [11] = 2002099
|  |  |  [12] = 7000081
|  |  |  [13] = 2006099
|  |  |  [14] = 1003099
|  |  |  [15] = 3006099
|  |  |  [16] = 3007099
|  |  |  [17] = 3008099
|  |  |  [18] = 3003099
|  |  |  [19] = 7000091
|  |  |  [2] = 7000051
|  |  |  [20] = 3004099
|  |  |  [21] = 3005099
|  |  |  [22] = 1004099
|  |  |  [23] = 3009099
|  |  |  [24] = 3010099
|  |  |  [25] = 3011099
|  |  |  [26] = 1006099
|  |  |  [27] = 3016099
|  |  |  [28] = 1010099
|  |  |  [29] = 5003099
|  |  |  [3] = 6010002
|  |  |  [30] = 1005099
|  |  |  [31] = 3012099
|  |  |  [32] = 3013099
|  |  |  [33] = 3014099
|  |  |  [34] = 3015099
|  |  |  [35] = 7000071
|  |  |  [36] = 1007099
|  |  |  [37] = 3019099
|  |  |  [4] = 1027099
|  |  |  [5] = 3001099
|  |  |  [6] = 1028099
|  |  |  [7] = 2001099
|  |  |  [8] = 7000061
|  |  |  [9] = 1001099
|  |  }
|  |  version = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>>>>>>>>>>>>>>>>>>>>> GS2CGuidanceInfo </color>
[core/table.lua:94]:-->Net Receive: travel.GS2CLoginTravelPartner = {
|  travel_partner = {
|  |  server_time = 1754034567
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CServerGradeInfo = {
|  days = 12
|  server_grade = 95
}
[core/table.lua:94]:-->Net Receive: scene.GS2CShowScene = {
|  map_id = 101000
|  scene_id = 12
|  scene_name = "帝都"
}
[core/global.lua:59]:<color=#ffeb04>CResCtrl.MoveToSecondary</color>
[core/table.lua:94]:-->Net Receive: task.GS2CLoginTask = {
|  shimen_status = 1
|  taskdata = {
|  |  [1] = {
|  |  |  acceptnpc = 5005
|  |  |  detaildesc = "看来教会的人对统帅部有着不可描叙的敌意！"
|  |  |  name = "野蛮人"
|  |  |  patrolinfo = {}
|  |  |  pickiteminfo = {}
|  |  |  placeinfo = {}
|  |  |  shapeinfo = {}
|  |  |  statusinfo = {
|  |  |  |  note = "CreateTask"
|  |  |  |  status = 2
|  |  |  }
|  |  |  submitnpc = 5005
|  |  |  target = 5005
|  |  |  targetdesc = "教会与统帅部"
|  |  |  taskid = 10384
|  |  |  taskitem = {}
|  |  |  tasktype = 1
|  |  |  traceinfo = {}
|  |  |  type = 2
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>login task</color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4e164120"
|  |  AssociatedPick = "function: 0x4e164180"
|  |  AssociatedSubmit = "function: 0x4e164150"
|  |  CreateDefalutData = "function: 0x4e162100"
|  |  GetChaptetFubenData = "function: 0x4e163e40"
|  |  GetProgressThing = "function: 0x4e1641e0"
|  |  GetRemainTime = "function: 0x4e162098"
|  |  GetStatus = "function: 0x4e164240"
|  |  GetTaskClientExtStrDic = "function: 0x4e1641b0"
|  |  GetTaskTypeSpriteteName = "function: 0x4e163e10"
|  |  GetTraceInfo = "function: 0x4e163f68"
|  |  GetTraceNpcType = "function: 0x4e163de0"
|  |  GetValue = "function: 0x4e163ea8"
|  |  IsAbandon = "function: 0x4e163f08"
|  |  IsAddEscortDynamicNpc = "function: 0x4e164578"
|  |  IsMissMengTask = "function: 0x4e1620c8"
|  |  IsPassChaterFuben = "function: 0x4e163e70"
|  |  IsTaskSpecityAction = "function: 0x4e163f38"
|  |  IsTaskSpecityCategory = "function: 0x4e1640f0"
|  |  New = "function: 0x4e168a70"
|  |  NewByData = "function: 0x4e161578"
|  |  RaiseProgressIdx = "function: 0x4e164210"
|  |  RefreshTask = "function: 0x4e163ed8"
|  |  ResetEndTime = "function: 0x4e162168"
|  |  SetStatus = "function: 0x4e1645a8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4e162068"
|  }
|  m_CData = {
|  |  ChapterFb = "1,7"
|  |  autoDoNextTask = 10023
|  |  clientExtStr = ""
|  |  name = "野蛮人"
|  |  submitNpcId = 5005
|  |  submitRewardStr = {
|  |  |  [1] = "R1384"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5005
|  |  autotype = 1
|  |  detaildesc = "看来教会的人对统帅部有着不可描叙的敌意！"
|  |  isdone = 0
|  |  name = "野蛮人"
|  |  patrolinfo = {}
|  |  pickiteminfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 5005
|  |  target = 5005
|  |  targetdesc = "教会与统帅部"
|  |  taskid = 10384
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: teach.GS2CTeachProgress = {}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshPartnerTask = {
|  partnertask_progress = {
|  |  [1] = {
|  |  |  parid = 313
|  |  |  status = 1
|  |  |  taskid = 62195
|  |  }
|  |  [2] = {
|  |  |  parid = 403
|  |  |  status = 1
|  |  |  taskid = 62169
|  |  }
|  |  [3] = {
|  |  |  parid = 501
|  |  |  status = 1
|  |  |  taskid = 62085
|  |  }
|  |  [4] = {
|  |  |  parid = 502
|  |  |  status = 1
|  |  |  taskid = 62090
|  |  }
|  |  [5] = {
|  |  |  parid = 302
|  |  |  status = 1
|  |  |  taskid = 62080
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: openui.GS2CLoginSchedule = {
|  day_task = {
|  |  [1] = 1016
|  |  [2] = 3005
|  }
}
[core/table.lua:94]:-->Net Receive: state.GS2CLoginState = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginHuodongInfo = {
|  convoyinfo = {
|  |  convoy_partner = 1503
|  |  free_time = 1
|  |  pool_info = {
|  |  |  [1] = {
|  |  |  |  level = 1
|  |  |  |  partnerid = 1503
|  |  |  |  pos = 1
|  |  |  |  rewardid = 1001
|  |  |  }
|  |  |  [2] = {
|  |  |  |  level = 2
|  |  |  |  partnerid = 1201
|  |  |  |  pos = 2
|  |  |  |  rewardid = 1002
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [3] = {
|  |  |  |  level = 3
|  |  |  |  partnerid = 1009
|  |  |  |  pos = 3
|  |  |  |  rewardid = 1003
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [4] = {
|  |  |  |  level = 4
|  |  |  |  partnerid = 1510
|  |  |  |  pos = 4
|  |  |  |  rewardid = 1004
|  |  |  |  weight = 25
|  |  |  }
|  |  |  [5] = {
|  |  |  |  level = 5
|  |  |  |  partnerid = 1020
|  |  |  |  pos = 5
|  |  |  |  rewardid = 1005
|  |  |  |  weight = 25
|  |  |  }
|  |  }
|  |  selected_pos = 1
|  |  target_npc = 5033
|  }
|  dailytrain = {}
|  hireinfo = {
|  |  [1] = {
|  |  |  parid = 501
|  |  |  times = 1
|  |  }
|  |  [2] = {
|  |  |  parid = 502
|  |  |  times = 1
|  |  }
|  |  [3] = {
|  |  |  parid = 403
|  |  |  times = 1
|  |  }
|  }
|  huntinfo = {}
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginChapterInfo = {
|  extrareward_info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 6
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = 1
|  |  |  level = 4
|  |  |  type = 1
|  |  }
|  }
|  finalchapter = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  level = 8
|  |  |  open = 1
|  |  |  type = 1
|  |  }
|  }
|  totalstar_info = {
|  |  [1] = {
|  |  |  chapter = 1
|  |  |  star = 21
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: handbook.GS2CLoginBookList = {
|  book_list = {
|  |  [1] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313131
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113131
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100313
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [2] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 314031
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 114031
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100403
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [3] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315011
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  id = 315012
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115011
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100501
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [4] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 315021
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 115021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100502
|  |  |  red_point = 1
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  |  [5] = {
|  |  |  chapter = {
|  |  |  |  [1] = {
|  |  |  |  |  id = 313021
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  condition = {
|  |  |  |  |  |  [1] = 113023
|  |  |  |  |  }
|  |  |  |  |  id = 313022
|  |  |  |  |  unlock = 1
|  |  |  |  }
|  |  |  }
|  |  |  condition = {
|  |  |  |  [1] = 113021
|  |  |  }
|  |  |  entry_name = 1
|  |  |  id = 100302
|  |  |  red_point = 3
|  |  |  repair = 1
|  |  |  show = 1
|  |  |  unlock = 1
|  |  }
|  }
|  red_points = {
|  |  [1] = {
|  |  |  book_type = 1
|  |  |  red_point = 1
|  |  }
|  |  [2] = {
|  |  |  book_type = 2
|  |  }
|  |  [3] = {
|  |  |  book_type = 3
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: team.GS2CInviteInfo = {}
[core/table.lua:94]:-->Net Receive: mail.GS2CLoginMail = {
|  simpleinfo = {
|  |  [1] = {
|  |  |  createtime = 1752549158
|  |  |  hasattach = 2
|  |  |  keeptime = 7776000
|  |  |  mailid = 1
|  |  |  opened = 1
|  |  |  subject = "喵小萌的来信"
|  |  |  title = "冲榜返利"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: friend.GS2CLoginFriend = {}
[core/global.lua:59]:<color=#ffeb04>好友版本一致，无需更新</color>
[core/table.lua:94]:-->Net Receive: friend.GS2CApplyList = {}
[core/table.lua:94]:-->Net Receive: friend.GS2CFriendSetting = {
|  setting = {
|  |  notify = 1
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CDrawCardUI = {}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcFightInfoList = {
|  info_list = {
|  |  [1] = {
|  |  |  npc_type = 10024
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [10] = {
|  |  |  npc_type = 10007
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [11] = {
|  |  |  npc_type = 10008
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [12] = {
|  |  |  npc_type = 10009
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [13] = {
|  |  |  npc_type = 10011
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [14] = {
|  |  |  npc_type = 10012
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [15] = {
|  |  |  npc_type = 10013
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [16] = {
|  |  |  npc_type = 10000
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [17] = {
|  |  |  npc_type = 10023
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [18] = {
|  |  |  npc_type = 10014
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [19] = {
|  |  |  npc_type = 10015
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [2] = {
|  |  |  npc_type = 10002
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [20] = {
|  |  |  npc_type = 10016
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [21] = {
|  |  |  npc_type = 10017
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [22] = {
|  |  |  npc_type = 10018
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [23] = {
|  |  |  npc_type = 10019
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [24] = {
|  |  |  npc_type = 10020
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [25] = {
|  |  |  npc_type = 10021
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [26] = {
|  |  |  npc_type = 10022
|  |  |  rewards = {
|  |  |  |  [1] = 1061
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [3] = {
|  |  |  npc_type = 10010
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [4] = {
|  |  |  npc_type = 10001
|  |  |  rewards = {
|  |  |  |  [1] = 1011
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [5] = {
|  |  |  rewards = {
|  |  |  |  [1] = 1021
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [6] = {
|  |  |  npc_type = 10003
|  |  |  rewards = {
|  |  |  |  [1] = 1031
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [7] = {
|  |  |  npc_type = 10004
|  |  |  rewards = {
|  |  |  |  [1] = 1041
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [8] = {
|  |  |  npc_type = 10005
|  |  |  rewards = {
|  |  |  |  [1] = 1051
|  |  |  }
|  |  |  total = 10
|  |  }
|  |  [9] = {
|  |  |  npc_type = 10006
|  |  |  rewards = {
|  |  |  |  [1] = 1001
|  |  |  }
|  |  |  total = 10
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CRefreshRewardBack = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 29433
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 29433
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
|  mask = "c"
}
[core/table.lua:94]:CNetCtrl解析mask: GS2CChargeGiftInfo = {
|  charge_card = {
|  |  [1] = {
|  |  |  next_time = 29433
|  |  |  type = "zsk"
|  |  }
|  |  [2] = {
|  |  |  next_time = 29433
|  |  |  type = "yk"
|  |  }
|  }
|  czjj_grade_list = {
|  |  [1] = {
|  |  |  key = "grade_gift1_20"
|  |  }
|  |  [10] = {
|  |  |  key = "grade_gift1_40"
|  |  }
|  |  [2] = {
|  |  |  key = "grade_gift1_60"
|  |  }
|  |  [3] = {
|  |  |  key = "grade_gift1_65"
|  |  }
|  |  [4] = {
|  |  |  key = "grade_gift1_75"
|  |  }
|  |  [5] = {
|  |  |  key = "grade_gift1_70"
|  |  }
|  |  [6] = {
|  |  |  key = "grade_gift1_50"
|  |  }
|  |  [7] = {
|  |  |  key = "grade_gift1_80"
|  |  }
|  |  [8] = {
|  |  |  key = "grade_gift1_55"
|  |  }
|  |  [9] = {
|  |  |  key = "grade_gift1_30"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: store.GS2CPayForColorCoinInfo = {
|  colorcoin_list = {
|  |  [1] = {
|  |  |  key = "goldcoinstore_1012"
|  |  }
|  |  [10] = {
|  |  |  key = "goldcoinstore_2011"
|  |  }
|  |  [11] = {
|  |  |  key = "goldcoinstore_2012"
|  |  }
|  |  [12] = {
|  |  |  key = "goldcoinstore_2013"
|  |  }
|  |  [13] = {
|  |  |  key = "goldcoinstore_2014"
|  |  }
|  |  [14] = {
|  |  |  key = "goldcoinstore_2015"
|  |  }
|  |  [15] = {
|  |  |  key = "goldcoinstore_1015"
|  |  }
|  |  [16] = {
|  |  |  key = "goldcoinstore_1005"
|  |  }
|  |  [17] = {
|  |  |  key = "goldcoinstore_1006"
|  |  }
|  |  [18] = {
|  |  |  key = "goldcoinstore_1007"
|  |  }
|  |  [19] = {
|  |  |  key = "goldcoinstore_1008"
|  |  }
|  |  [2] = {
|  |  |  key = "goldcoinstore_1013"
|  |  }
|  |  [20] = {
|  |  |  key = "goldcoinstore_1009"
|  |  }
|  |  [21] = {
|  |  |  key = "goldcoinstore_1010"
|  |  }
|  |  [22] = {
|  |  |  key = "goldcoinstore_1011"
|  |  }
|  |  [3] = {
|  |  |  key = "goldcoinstore_1014"
|  |  }
|  |  [4] = {
|  |  |  key = "goldcoinstore_2005"
|  |  }
|  |  [5] = {
|  |  |  key = "goldcoinstore_2006"
|  |  }
|  |  [6] = {
|  |  |  key = "goldcoinstore_2007"
|  |  }
|  |  [7] = {
|  |  |  key = "goldcoinstore_2008"
|  |  }
|  |  [8] = {
|  |  |  key = "goldcoinstore_2009"
|  |  }
|  |  [9] = {
|  |  |  key = "goldcoinstore_2010"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeRewrad = {
|  end_time = 1751813939
|  start_time = 1751727600
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginTrapmine = {
|  rare_monster = {
|  |  [1] = {
|  |  |  map_id = 202000
|  |  |  partypes = {
|  |  |  |  [1] = 312
|  |  |  |  [2] = 401
|  |  |  |  [3] = 313
|  |  |  }
|  |  }
|  |  [2] = {
|  |  |  map_id = 204000
|  |  |  partypes = {
|  |  |  |  [1] = 404
|  |  |  |  [2] = 402
|  |  |  |  [3] = 403
|  |  |  }
|  |  }
|  |  [3] = {
|  |  |  map_id = 205000
|  |  |  partypes = {
|  |  |  |  [1] = 508
|  |  |  |  [2] = 509
|  |  |  |  [3] = 514
|  |  |  }
|  |  }
|  |  [4] = {
|  |  |  map_id = 206000
|  |  |  partypes = {
|  |  |  |  [1] = 413
|  |  |  |  [2] = 414
|  |  |  |  [3] = 410
|  |  |  }
|  |  }
|  |  [5] = {
|  |  |  map_id = 210400
|  |  |  partypes = {
|  |  |  |  [1] = 504
|  |  |  |  [2] = 506
|  |  |  |  [3] = 507
|  |  |  }
|  |  }
|  |  [6] = {
|  |  |  map_id = 200000
|  |  |  partypes = {
|  |  |  |  [1] = 416
|  |  |  |  [2] = 417
|  |  |  |  [3] = 502
|  |  |  }
|  |  }
|  |  [7] = {
|  |  |  map_id = 201000
|  |  |  partypes = {
|  |  |  |  [1] = 301
|  |  |  |  [2] = 302
|  |  |  |  [3] = 311
|  |  |  }
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliReddot = {
|  cday = 1
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CNewFieldBoss = {
|  bossid = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraWarState = {
|  state = 1
|  time = 1754130352
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CTerraQueueStatus = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CDailySignInfo = {
|  sign_info = {
|  |  [1] = {
|  |  |  key = "week"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2COnlineGift = {
|  onlinetime = 1921
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CHeroBoxRecord = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeScore = {
|  cur_id = 1
|  end_time = 1752505139
|  score_info = {}
|  start_time = 1751209200
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayBuy = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CHistoryCharge = {}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFirstChargeUI = {
|  bOpen = true
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliPoint = {
|  point = 4
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawCnt = {
|  cnt = 3
}
[core/table.lua:94]:-->Net Receive: fuli.GS2CFuliTime = {
|  endtime = 1752505139
|  starttime = 1751209200
}
[core/table.lua:94]:-->Net Receive: house.GS2CEnterHouse = {
|  buff_info = {}
|  furniture_info = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  lock_status = 1
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  type = 2
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  type = 3
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  type = 4
|  |  }
|  |  [5] = {
|  |  |  level = 1
|  |  |  type = 5
|  |  }
|  }
|  handle_type = 1
|  max_train = 1
|  max_warm_degree = 1000
|  owner_pid = 10008
|  partner_info = {
|  |  [1] = {
|  |  |  coin = 2700
|  |  |  type = 1001
|  |  }
|  |  [2] = {
|  |  |  type = 1003
|  |  }
|  }
|  warm_degree = 212
}
[core/table.lua:94]:-->Net Receive: house.GS2CPartnerExchangeUI = {
|  handle_type = 1
|  love_cnt = 10
|  max_gift_cnt = 10
|  max_love_cnt = 10
|  partner_gift_cnt = 10
}
[core/table.lua:94]:-->Net Receive: house.GS2COpenWorkDesk = {
|  desk_info = {
|  |  [1] = {
|  |  |  lock_status = 1
|  |  |  pos = 1
|  |  |  status = 1
|  |  }
|  |  [2] = {
|  |  |  pos = 2
|  |  |  status = 1
|  |  }
|  |  [3] = {
|  |  |  pos = 3
|  |  |  status = 1
|  |  }
|  |  [4] = {
|  |  |  lock_status = 1
|  |  |  pos = 4
|  |  |  status = 1
|  |  }
|  }
|  handle_type = 1
|  owner_pid = 10008
}
[core/table.lua:94]:-->Net Receive: player.GS2CTodayInfo = {
|  info = {
|  |  energy_receive = 1
|  |  mask = "8"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: todyInfo = {
|  energy_receive = 1
}
[core/table.lua:94]:-->Net Receive: player.GS2CGameShare = {
|  game_share = {
|  |  [1] = {
|  |  |  type = "draw_card_share"
|  |  }
|  |  [2] = {
|  |  |  type = "skin_share"
|  |  }
|  |  [3] = {
|  |  |  type = "picture_share"
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: huodong.GS2CLoginRewardInfo = {
|  breed_val = 7
|  login_day = 9
|  rewarded_day = 511
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 2
|  |  |  |  [2] = 3
|  |  |  }
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  mask = "1997c2e0000000"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  give_org_equip = {}
|  give_org_wish = {}
|  is_equip_wish = 0
|  is_org_wish = 0
|  org_build_status = 0
|  org_build_time = 0
|  org_id = 0
|  org_leader = ""
|  org_level = 0
|  org_pos = 0
|  org_red_packet = 0
|  org_sign_reward = 0
|  org_status = 0
|  orgname = ""
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CSevenDayMain = {
|  server_day = 25
}
[core/table.lua:94]:-->Net Receive: task.GS2CLoginAchieveTask = {
|  info = {
|  |  [1] = {
|  |  |  achievetype = 1
|  |  |  degree = 1
|  |  |  describe = "领取1次在线奖励"
|  |  |  name = "在线奖励"
|  |  |  target = 1
|  |  |  taskid = 31001
|  |  }
|  |  [2] = {
|  |  |  achievetype = 2
|  |  |  degree = 1
|  |  |  describe = "穿戴两件符文"
|  |  |  name = "穿戴符文（2）"
|  |  |  target = 2
|  |  |  taskid = 31533
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>LoginAchieveTask</color>
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterScene = {
|  eid = 27
|  pos_info = {
|  |  face_y = 357734
|  |  x = 48000
|  |  y = 23677
|  }
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04>result  nil</color>
[core/global.lua:59]:<color=#ffeb04>=00000=  userdata: 0x6f3bb270 nil</color>
[logic/ui/CSprite.lua:159]:DynamicSprite dSprInfo    Item    10012
[logic/ui/CViewBase.lua:125]:CMainMenuView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/global.lua:59]:<color=#ffeb04>读取地图： 1020</color>
[core/global.lua:59]:<color=#ffeb04>Unity2017.4.30f1 localScale</color>
[logic/ui/CViewCtrl.lua:104]:CLoginView     CloseView
[logic/base/CResCtrl.lua:599]:res gc step start!
[logic/ui/CViewCtrl.lua:104]:CSelectServerView     CloseView
[core/global.lua:59]:<color=#ffeb04>地图加载完成: 1020 ,当前地图: nil</color>
[core/table.lua:94]:Table = {}
[core/table.lua:94]:-->Net Receive: item.GS2CLoginItem = {
|  itemdata = {
|  |  [1] = {
|  |  |  amount = 3
|  |  |  create_time = 1752740705
|  |  |  id = 1
|  |  |  itemlevel = 2
|  |  |  name = "一星云母"
|  |  |  sid = 14031
|  |  }
|  |  [10] = {
|  |  |  amount = 1
|  |  |  create_time = 1753682159
|  |  |  id = 16
|  |  |  itemlevel = 2
|  |  |  name = "5级绯红宝石"
|  |  |  sid = 18004
|  |  }
|  |  [11] = {
|  |  |  amount = 2
|  |  |  create_time = 1753682390
|  |  |  id = 9
|  |  |  itemlevel = 2
|  |  |  name = "3级绯红宝石"
|  |  |  sid = 18002
|  |  }
|  |  [12] = {
|  |  |  amount = 1
|  |  |  create_time = 1753682159
|  |  |  id = 10
|  |  |  itemlevel = 2
|  |  |  name = "5级八云宝石"
|  |  |  sid = 18104
|  |  }
|  |  [13] = {
|  |  |  amount = 1
|  |  |  create_time = 1753781899
|  |  |  id = 11
|  |  |  itemlevel = 2
|  |  |  name = "1级疾风宝石"
|  |  |  sid = 18500
|  |  }
|  |  [14] = {
|  |  |  amount = 1
|  |  |  create_time = 1753682159
|  |  |  id = 12
|  |  |  itemlevel = 2
|  |  |  name = "5级双生宝石"
|  |  |  sid = 18204
|  |  }
|  |  [15] = {
|  |  |  amount = 1
|  |  |  create_time = 1753682159
|  |  |  id = 13
|  |  |  itemlevel = 2
|  |  |  name = "5级疾风宝石"
|  |  |  sid = 18504
|  |  }
|  |  [16] = {
|  |  |  amount = 1
|  |  |  create_time = 1753682159
|  |  |  id = 14
|  |  |  itemlevel = 2
|  |  |  name = "5级翠星宝石"
|  |  |  sid = 18404
|  |  }
|  |  [17] = {
|  |  |  amount = 1
|  |  |  create_time = 1753682159
|  |  |  id = 15
|  |  |  itemlevel = 2
|  |  |  name = "5级黄金宝石"
|  |  |  sid = 18304
|  |  }
|  |  [18] = {
|  |  |  amount = 1
|  |  |  create_time = 1751883929
|  |  |  id = 17
|  |  |  itemlevel = 1
|  |  |  name = "攻击符文"
|  |  |  partner_equip = {
|  |  |  |  parid = 1
|  |  |  |  stone_level = 1
|  |  |  }
|  |  |  sid = 6101005
|  |  }
|  |  [19] = {
|  |  |  amount = 1
|  |  |  create_time = 1753682256
|  |  |  id = 20
|  |  |  itemlevel = 3
|  |  |  name = "魂夕碎片"
|  |  |  sid = 20508
|  |  }
|  |  [2] = {
|  |  |  amount = 20
|  |  |  create_time = 1753781903
|  |  |  id = 2
|  |  |  itemlevel = 5
|  |  |  name = "扫荡券"
|  |  |  sid = 10030
|  |  }
|  |  [20] = {
|  |  |  amount = 1
|  |  |  create_time = 1753682269
|  |  |  id = 18
|  |  |  itemlevel = 3
|  |  |  name = "松姑子碎片"
|  |  |  sid = 20417
|  |  }
|  |  [21] = {
|  |  |  amount = 1
|  |  |  create_time = 1753682052
|  |  |  id = 19
|  |  |  itemlevel = 3
|  |  |  name = "吞天碎片"
|  |  |  sid = 20505
|  |  }
|  |  [22] = {
|  |  |  amount = 1
|  |  |  create_time = 1753781901
|  |  |  id = 22
|  |  |  itemlevel = 4
|  |  |  name = "梦觉书·高"
|  |  |  sid = 27403
|  |  }
|  |  [23] = {
|  |  |  amount = 3
|  |  |  create_time = 1753781897
|  |  |  id = 21
|  |  |  itemlevel = 3
|  |  |  name = "梦觉书·低"
|  |  |  sid = 27402
|  |  }
|  |  [24] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 57
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 1
|  |  |  |  stone_sid = 3100000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 24
|  |  |  itemlevel = 1
|  |  |  name = "练习红魔钺"
|  |  |  power = 57
|  |  |  sid = 2100000
|  |  }
|  |  [25] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 121
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 24
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 2
|  |  |  |  stone_sid = 4000000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 27
|  |  |  itemlevel = 1
|  |  |  name = "练习飘逸巾"
|  |  |  power = 24
|  |  |  sid = 2200000
|  |  }
|  |  [26] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 32
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 3
|  |  |  |  stone_sid = 4110000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 25
|  |  |  itemlevel = 1
|  |  |  name = "练习絮语衣"
|  |  |  power = 32
|  |  |  sid = 2320000
|  |  }
|  |  [27] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "attack"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 19
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 4
|  |  |  |  stone_sid = 4200000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 28
|  |  |  itemlevel = 1
|  |  |  name = "练习草织戒"
|  |  |  power = 19
|  |  |  sid = 2400000
|  |  }
|  |  [28] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "maxhp"
|  |  |  |  |  value = 364
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 72
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 5
|  |  |  |  stone_sid = 4310000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 26
|  |  |  itemlevel = 1
|  |  |  name = "练习离叶腰"
|  |  |  power = 72
|  |  |  sid = 2520000
|  |  }
|  |  [29] = {
|  |  |  amount = 1
|  |  |  apply_info = {
|  |  |  |  [1] = {
|  |  |  |  |  key = "speed"
|  |  |  |  |  value = 33
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  key = "defense"
|  |  |  |  |  value = 11
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  key = "equip_power"
|  |  |  |  |  value = 77
|  |  |  |  }
|  |  |  }
|  |  |  create_time = 1751861124
|  |  |  equip_info = {
|  |  |  |  pos = 6
|  |  |  |  stone_sid = 4410000
|  |  |  |  strength_attr = {
|  |  |  |  |  [1] = {
|  |  |  |  |  |  key = "level"
|  |  |  |  |  }
|  |  |  |  }
|  |  |  }
|  |  |  id = 29
|  |  |  itemlevel = 1
|  |  |  name = "练习青藤鞋"
|  |  |  power = 77
|  |  |  sid = 2620000
|  |  }
|  |  [3] = {
|  |  |  amount = 1
|  |  |  create_time = 1752734064
|  |  |  id = 3
|  |  |  itemlevel = 2
|  |  |  name = "3级符石礼包"
|  |  |  sid = 14103
|  |  }
|  |  [4] = {
|  |  |  amount = 14
|  |  |  create_time = 1753682662
|  |  |  id = 4
|  |  |  itemlevel = 2
|  |  |  name = "深蓝琥珀"
|  |  |  sid = 14021
|  |  }
|  |  [5] = {
|  |  |  amount = 5
|  |  |  create_time = 1753667289
|  |  |  id = 5
|  |  |  itemlevel = 4
|  |  |  name = "万能碎片"
|  |  |  sid = 14002
|  |  }
|  |  [6] = {
|  |  |  amount = 1
|  |  |  create_time = 1753682119
|  |  |  id = 6
|  |  |  itemlevel = 4
|  |  |  name = "50级橙武礼包"
|  |  |  sid = 12083
|  |  }
|  |  [7] = {
|  |  |  amount = 3
|  |  |  create_time = 1753682427
|  |  |  id = 7
|  |  |  itemlevel = 2
|  |  |  name = "鲜肉包"
|  |  |  sid = 14001
|  |  }
|  |  [8] = {
|  |  |  amount = 30
|  |  |  create_time = 1753781915
|  |  |  id = 8
|  |  |  itemlevel = 3
|  |  |  name = "焦糖包"
|  |  |  sid = 14011
|  |  }
|  |  [9] = {
|  |  |  amount = 20
|  |  |  create_time = 1754021115
|  |  |  id = 23
|  |  |  itemlevel = 3
|  |  |  name = "淬灵云晶"
|  |  |  sid = 11101
|  |  }
|  }
}
[core/global.lua:59]:<color=#ffeb04>loginitem</color>
[core/table.lua:94]:-->Net Receive: item.GS2CFuWenPlanName = {}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartnerList = {
|  partner_list = {
|  |  [1] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 482
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 99
|  |  |  equip_list = {
|  |  |  |  [1] = 17
|  |  |  }
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 14000
|  |  |  grade = 12
|  |  |  hp = 3402
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 3402
|  |  |  model_info = {
|  |  |  |  shape = 302
|  |  |  |  skin = 203020
|  |  |  }
|  |  |  name = "重华"
|  |  |  parid = 1
|  |  |  partner_type = 302
|  |  |  patahp = 3402
|  |  |  power = 1187
|  |  |  power_rank = 1
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 30203
|  |  |  |  }
|  |  |  }
|  |  |  speed = 420
|  |  |  star = 1
|  |  }
|  |  [2] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 371
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 85
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 9180
|  |  |  grade = 9
|  |  |  hp = 2884
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 2884
|  |  |  model_info = {
|  |  |  |  shape = 502
|  |  |  |  skin = 205020
|  |  |  }
|  |  |  name = "马面面"
|  |  |  parid = 2
|  |  |  partner_type = 502
|  |  |  patahp = 2884
|  |  |  power = 964
|  |  |  power_rank = 2
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 500
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50201
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50202
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50203
|  |  |  |  }
|  |  |  }
|  |  |  speed = 315
|  |  |  star = 1
|  |  }
|  |  [3] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 349
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 600
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 80
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 6600
|  |  |  grade = 7
|  |  |  hp = 2895
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 2895
|  |  |  model_info = {
|  |  |  |  shape = 403
|  |  |  |  skin = 204030
|  |  |  }
|  |  |  name = "蛇姬"
|  |  |  parid = 3
|  |  |  partner_type = 403
|  |  |  patahp = 2895
|  |  |  power = 932
|  |  |  power_rank = 2
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 600
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 40303
|  |  |  |  }
|  |  |  }
|  |  |  speed = 735
|  |  |  star = 1
|  |  }
|  |  [4] = {
|  |  |  abnormal_attr_ratio = 600
|  |  |  amount = 1
|  |  |  attack = 273
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 800
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 67
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  grade = 1
|  |  |  mask = "fffffefe"
|  |  |  max_hp = 1862
|  |  |  model_info = {
|  |  |  |  shape = 313
|  |  |  |  skin = 203130
|  |  |  }
|  |  |  name = "檀"
|  |  |  parid = 4
|  |  |  partner_type = 313
|  |  |  patahp = 1862
|  |  |  power = 879
|  |  |  power_rank = 2
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 700
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31301
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31302
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 31303
|  |  |  |  }
|  |  |  }
|  |  |  speed = 665
|  |  |  star = 2
|  |  }
|  |  [5] = {
|  |  |  abnormal_attr_ratio = 400
|  |  |  amount = 1
|  |  |  attack = 317
|  |  |  critical_damage = 15000
|  |  |  critical_ratio = 1000
|  |  |  cure_critical_ratio = 500
|  |  |  defense = 56
|  |  |  equip_plan = {
|  |  |  |  [1] = {
|  |  |  |  |  plan_id = 1
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  plan_id = 2
|  |  |  |  }
|  |  |  }
|  |  |  equip_plan_id = 1
|  |  |  exp = 1600
|  |  |  grade = 3
|  |  |  hp = 2020
|  |  |  mask = "fffffffe"
|  |  |  max_hp = 2020
|  |  |  model_info = {
|  |  |  |  shape = 501
|  |  |  |  skin = 205010
|  |  |  }
|  |  |  name = "阿坊"
|  |  |  parid = 5
|  |  |  partner_type = 501
|  |  |  patahp = 2020
|  |  |  power = 734
|  |  |  power_rank = 2
|  |  |  res_abnormal_ratio = 500
|  |  |  res_critical_ratio = 400
|  |  |  skill = {
|  |  |  |  [1] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50101
|  |  |  |  }
|  |  |  |  [2] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50102
|  |  |  |  }
|  |  |  |  [3] = {
|  |  |  |  |  level = 1
|  |  |  |  |  sk = 50103
|  |  |  |  }
|  |  |  }
|  |  |  speed = 70
|  |  |  star = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginPartner = {
|  fight_info = {
|  |  [1] = {
|  |  |  parid = 1
|  |  |  pos = 1
|  |  }
|  |  [2] = {
|  |  |  parid = 2
|  |  |  pos = 2
|  |  }
|  |  [3] = {
|  |  |  parid = 3
|  |  |  pos = 3
|  |  }
|  |  [4] = {
|  |  |  parid = 5
|  |  |  pos = 4
|  |  }
|  }
|  owned_equip_list = {
|  |  [1] = 6101001
|  }
|  owned_partner_list = {
|  |  [1] = 313
|  |  [2] = 403
|  |  [3] = 501
|  |  [4] = 502
|  |  [5] = 302
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPicturePosList = {}
[core/table.lua:94]:-->Net Receive: skill.GS2CLoginSkill = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[logic/skill/CSkillCtrl.lua:112]:技能数据登陆协议返回:
[core/table.lua:94]:Table = {
|  cultivate = {
|  |  [1] = {
|  |  |  sk = 4000
|  |  }
|  |  [2] = {
|  |  |  sk = 4001
|  |  }
|  |  [3] = {
|  |  |  sk = 4002
|  |  }
|  |  [4] = {
|  |  |  sk = 4003
|  |  }
|  |  [5] = {
|  |  |  sk = 4004
|  |  }
|  |  [6] = {
|  |  |  sk = 4005
|  |  }
|  |  [7] = {
|  |  |  sk = 4006
|  |  }
|  |  [8] = {
|  |  |  sk = 4007
|  |  }
|  |  [9] = {
|  |  |  sk = 4008
|  |  }
|  }
|  school = {
|  |  [1] = {
|  |  |  level = 1
|  |  |  needcost = 5
|  |  |  sk = 3001
|  |  |  type = 1
|  |  }
|  |  [2] = {
|  |  |  level = 1
|  |  |  needcost = 10
|  |  |  sk = 3002
|  |  |  type = 1
|  |  }
|  |  [3] = {
|  |  |  level = 1
|  |  |  needcost = 15
|  |  |  sk = 3004
|  |  |  type = 1
|  |  }
|  |  [4] = {
|  |  |  level = 1
|  |  |  needcost = 25
|  |  |  sk = 3005
|  |  |  type = 1
|  |  }
|  |  [5] = {
|  |  |  needcost = 18
|  |  |  sk = 3006
|  |  |  type = 1
|  |  }
|  |  [6] = {
|  |  |  needcost = 18
|  |  |  sk = 3007
|  |  |  type = 1
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CLoginParSoulPlan = {}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 550
|  |  attack = 324
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  mask = "87ff00"
|  |  max_hp = 3091
|  |  power = 963
|  |  res_abnormal_ratio = 550
|  |  res_critical_ratio = 500
|  |  speed = 753
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 550
|  attack = 324
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  hp = 0
|  max_hp = 3091
|  power = 963
|  res_abnormal_ratio = 550
|  res_critical_ratio = 500
|  speed = 753
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1102
|  |  |  }
|  |  |  name = "峰馆长"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 2
|  |  npctype = 5040
|  }
|  eid = 1
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 1
|  pos_info = {
|  |  x = 31800
|  |  y = 17600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1102
|  }
|  name = "峰馆长"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1508
|  |  |  }
|  |  |  name = "小叶"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 5
|  |  npctype = 5041
|  }
|  eid = 2
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 2
|  pos_info = {
|  |  x = 40000
|  |  y = 14600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1508
|  }
|  name = "小叶"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 508
|  |  |  }
|  |  |  name = "魂夕"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 81
|  |  npctype = 5008
|  }
|  eid = 16
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 16
|  pos_info = {
|  |  x = 24000
|  |  y = 12900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 508
|  }
|  name = "魂夕"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1016
|  |  |  }
|  |  |  name = "水生"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 196
|  |  npctype = 5009
|  }
|  eid = 17
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 17
|  pos_info = {
|  |  x = 24000
|  |  y = 22000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1016
|  }
|  name = "水生"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点11(中)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30777
|  |  npctype = 11
|  |  owner = "领主: "
|  }
|  eid = 42
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 42
|  pos_info = {
|  |  x = 29000
|  |  y = 25000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点11(中)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点10(小)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30775
|  |  npctype = 10
|  |  owner = "领主: "
|  }
|  eid = 41
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 41
|  pos_info = {
|  |  x = 27000
|  |  y = 18000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点10(小)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 417
|  |  |  }
|  |  |  name = "松姑子"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 220
|  |  npctype = 5019
|  }
|  eid = 21
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 21
|  pos_info = {
|  |  x = 42300
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 417
|  }
|  name = "松姑子"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点12(小)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30779
|  |  npctype = 12
|  |  owner = "领主: "
|  }
|  eid = 22
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 22
|  pos_info = {
|  |  x = 34000
|  |  y = 13000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点12(小)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1012
|  |  |  }
|  |  |  name = "克瑞斯汀"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 24
|  |  npctype = 5048
|  }
|  eid = 7
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 7
|  pos_info = {
|  |  x = 20200
|  |  y = 21100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1012
|  }
|  name = "克瑞斯汀"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点9(中)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30773
|  |  npctype = 9
|  |  owner = "领主: "
|  }
|  eid = 40
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 40
|  pos_info = {
|  |  x = 30000
|  |  y = 15000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点9(中)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点15(小)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30785
|  |  npctype = 15
|  |  owner = "领主: "
|  }
|  eid = 25
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 25
|  pos_info = {
|  |  x = 45000
|  |  y = 16000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点15(小)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点16(中)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30787
|  |  npctype = 16
|  |  owner = "领主: "
|  }
|  eid = 26
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 26
|  pos_info = {
|  |  x = 45000
|  |  y = 25000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点16(中)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点6(小)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30767
|  |  npctype = 6
|  |  owner = "领主: "
|  }
|  eid = 37
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 37
|  pos_info = {
|  |  x = 22000
|  |  y = 12000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点6(小)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1010
|  |  |  }
|  |  |  name = "丽丝"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 69
|  |  npctype = 5004
|  }
|  eid = 12
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 12
|  pos_info = {
|  |  x = 45600
|  |  y = 26900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1010
|  }
|  name = "丽丝"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1011
|  |  |  }
|  |  |  name = "遥"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 72
|  |  npctype = 5005
|  }
|  eid = 13
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 13
|  pos_info = {
|  |  x = 45500
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1011
|  }
|  name = "遥"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 414
|  |  |  }
|  |  |  name = "蓝堂昼"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 75
|  |  npctype = 5006
|  }
|  eid = 14
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 14
|  pos_info = {
|  |  x = 28000
|  |  y = 14500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 414
|  }
|  name = "蓝堂昼"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 311
|  |  |  }
|  |  |  name = "花常酒"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 78
|  |  npctype = 5007
|  }
|  eid = 15
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 15
|  pos_info = {
|  |  x = 35600
|  |  y = 13400
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 311
|  }
|  name = "花常酒"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: title.GS2CTitleInfoList = {
|  infos = {
|  |  [1] = {
|  |  |  name = "8段"
|  |  |  tid = 1008
|  |  }
|  |  [10] = {
|  |  |  name = "应用宝贵族"
|  |  |  tid = 1017
|  |  }
|  |  [11] = {
|  |  |  name = "王者公会"
|  |  |  tid = 1018
|  |  }
|  |  [12] = {
|  |  |  name = "王者公会会长"
|  |  |  tid = 1019
|  |  }
|  |  [13] = {
|  |  |  name = "黄金公会"
|  |  |  tid = 1020
|  |  }
|  |  [14] = {
|  |  |  name = "黄金公会会长"
|  |  |  tid = 1021
|  |  }
|  |  [15] = {
|  |  |  name = "白银公会"
|  |  |  tid = 1022
|  |  }
|  |  [16] = {
|  |  |  name = "白银公会会长"
|  |  |  tid = 1023
|  |  }
|  |  [17] = {
|  |  |  name = "明日之星"
|  |  |  tid = 1024
|  |  }
|  |  [18] = {
|  |  |  name = "独孤求败"
|  |  |  tid = 1025
|  |  }
|  |  [19] = {
|  |  |  name = "谁与争锋"
|  |  |  tid = 1026
|  |  }
|  |  [2] = {
|  |  |  name = "测-贵族宝"
|  |  |  tid = 1009
|  |  }
|  |  [20] = {
|  |  |  name = "所向披靡"
|  |  |  tid = 1027
|  |  }
|  |  [21] = {
|  |  |  name = "地牢终结者"
|  |  |  tid = 1028
|  |  }
|  |  [22] = {
|  |  |  name = "地牢征服者"
|  |  |  tid = 1029
|  |  }
|  |  [23] = {
|  |  |  name = "地牢破坏者"
|  |  |  tid = 1030
|  |  }
|  |  [24] = {
|  |  |  name = "眯眯眼是变态"
|  |  |  tid = 1031
|  |  }
|  |  [25] = {
|  |  |  name = "邻家妹妹"
|  |  |  tid = 1032
|  |  }
|  |  [26] = {
|  |  |  name = "兄控"
|  |  |  tid = 1033
|  |  }
|  |  [27] = {
|  |  |  name = "农夫三拳"
|  |  |  tid = 1034
|  |  }
|  |  [28] = {
|  |  |  name = "赌神"
|  |  |  tid = 1035
|  |  }
|  |  [29] = {
|  |  |  name = "神之右手"
|  |  |  tid = 1036
|  |  }
|  |  [3] = {
|  |  |  name = "测-我就是托"
|  |  |  tid = 1010
|  |  }
|  |  [30] = {
|  |  |  name = "茶会噩梦"
|  |  |  tid = 1037
|  |  }
|  |  [31] = {
|  |  |  name = "我是傲娇"
|  |  |  tid = 1038
|  |  }
|  |  [32] = {
|  |  |  name = "闷骚美男子"
|  |  |  tid = 1039
|  |  }
|  |  [33] = {
|  |  |  name = "脸炮"
|  |  |  tid = 1040
|  |  }
|  |  [34] = {
|  |  |  name = "妹控"
|  |  |  tid = 1041
|  |  }
|  |  [35] = {
|  |  |  name = "守夜人"
|  |  |  tid = 1042
|  |  }
|  |  [36] = {
|  |  |  name = "泡泡娘"
|  |  |  tid = 1043
|  |  }
|  |  [37] = {
|  |  |  name = "姐控"
|  |  |  tid = 1044
|  |  }
|  |  [38] = {
|  |  |  name = "迷妹"
|  |  |  tid = 1045
|  |  }
|  |  [39] = {
|  |  |  name = "小天使"
|  |  |  tid = 1046
|  |  }
|  |  [4] = {
|  |  |  name = "测-粗壮大腿"
|  |  |  tid = 1011
|  |  }
|  |  [40] = {
|  |  |  name = "欺诈兔子"
|  |  |  tid = 1047
|  |  }
|  |  [41] = {
|  |  |  name = "天然呆"
|  |  |  tid = 1048
|  |  }
|  |  [42] = {
|  |  |  name = "开局一条狗"
|  |  |  tid = 1049
|  |  }
|  |  [43] = {
|  |  |  name = "种竹专业户"
|  |  |  tid = 1050
|  |  }
|  |  [44] = {
|  |  |  name = "顾家好男人"
|  |  |  tid = 1051
|  |  }
|  |  [45] = {
|  |  |  name = "无敌小钢炮"
|  |  |  tid = 1052
|  |  }
|  |  [46] = {
|  |  |  name = "小豆丁"
|  |  |  tid = 1053
|  |  }
|  |  [47] = {
|  |  |  name = "浪里白条"
|  |  |  tid = 1054
|  |  }
|  |  [48] = {
|  |  |  name = "话唠"
|  |  |  tid = 1055
|  |  }
|  |  [49] = {
|  |  |  name = "角落种蘑菇"
|  |  |  tid = 1056
|  |  }
|  |  [5] = {
|  |  |  name = "攻防战第一公会"
|  |  |  tid = 1012
|  |  }
|  |  [50] = {
|  |  |  name = "天然疯"
|  |  |  tid = 1057
|  |  }
|  |  [51] = {
|  |  |  name = "吃货"
|  |  |  tid = 1058
|  |  }
|  |  [52] = {
|  |  |  name = "控制狂"
|  |  |  tid = 1059
|  |  }
|  |  [53] = {
|  |  |  name = "女王大人"
|  |  |  tid = 1060
|  |  }
|  |  [54] = {
|  |  |  name = "八卦小王子"
|  |  |  tid = 1061
|  |  }
|  |  [55] = {
|  |  |  name = "三无"
|  |  |  tid = 1062
|  |  }
|  |  [56] = {
|  |  |  name = "养得累"
|  |  |  tid = 1063
|  |  }
|  |  [57] = {
|  |  |  name = "扑街之王"
|  |  |  tid = 1064
|  |  }
|  |  [58] = {
|  |  |  name = "抖S"
|  |  |  tid = 1065
|  |  }
|  |  [59] = {
|  |  |  name = "工作狂人"
|  |  |  tid = 1066
|  |  }
|  |  [6] = {
|  |  |  name = "攻防战第一司令"
|  |  |  tid = 1013
|  |  }
|  |  [60] = {
|  |  |  name = "精分中毒患者"
|  |  |  tid = 1067
|  |  }
|  |  [61] = {
|  |  |  name = "神神叨叨"
|  |  |  tid = 1068
|  |  }
|  |  [62] = {
|  |  |  name = "浮夸之神"
|  |  |  tid = 1069
|  |  }
|  |  [63] = {
|  |  |  name = "抖M"
|  |  |  tid = 1070
|  |  }
|  |  [64] = {
|  |  |  name = "自恋狂"
|  |  |  tid = 1071
|  |  }
|  |  [65] = {
|  |  |  name = "天然黑"
|  |  |  tid = 1072
|  |  }
|  |  [66] = {
|  |  |  name = "为所欲为"
|  |  |  tid = 1073
|  |  }
|  |  [67] = {
|  |  |  name = "氪金大佬"
|  |  |  tid = 1074
|  |  }
|  |  [68] = {
|  |  |  name = "应用宝帝王"
|  |  |  tid = 1075
|  |  }
|  |  [69] = {
|  |  |  name = "应用宝至尊"
|  |  |  tid = 1076
|  |  }
|  |  [7] = {
|  |  |  name = "梦魇之主"
|  |  |  tid = 1014
|  |  }
|  |  [70] = {
|  |  |  name = "公会新人"
|  |  |  tid = 1077
|  |  }
|  |  [71] = {
|  |  |  name = "公会成员"
|  |  |  tid = 1078
|  |  }
|  |  [72] = {
|  |  |  name = "公会精英"
|  |  |  tid = 1079
|  |  }
|  |  [73] = {
|  |  |  name = "公会副会"
|  |  |  tid = 1080
|  |  }
|  |  [74] = {
|  |  |  name = "公会会长"
|  |  |  tid = 1081
|  |  }
|  |  [75] = {
|  |  |  name = "情侣专属称谓"
|  |  |  tid = 1082
|  |  }
|  |  [76] = {
|  |  |  name = "命运之子"
|  |  |  tid = 1083
|  |  }
|  |  [77] = {
|  |  |  name = "不做大多数"
|  |  |  tid = 1084
|  |  }
|  |  [78] = {
|  |  |  name = "1段"
|  |  |  tid = 1001
|  |  }
|  |  [79] = {
|  |  |  name = "2段"
|  |  |  tid = 1002
|  |  }
|  |  [8] = {
|  |  |  name = "梦魇骑士"
|  |  |  tid = 1015
|  |  }
|  |  [80] = {
|  |  |  name = "3段"
|  |  |  tid = 1003
|  |  }
|  |  [81] = {
|  |  |  name = "4段"
|  |  |  tid = 1004
|  |  }
|  |  [82] = {
|  |  |  name = "5段"
|  |  |  tid = 1005
|  |  }
|  |  [83] = {
|  |  |  name = "6段"
|  |  |  tid = 1006
|  |  }
|  |  [84] = {
|  |  |  name = "7段"
|  |  |  tid = 1007
|  |  }
|  |  [9] = {
|  |  |  name = "梦魇猎人"
|  |  |  tid = 1016
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034567
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:49:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/base/CResCtrl.lua:626]:res gc finish!
[logic/platform/CSdkCtrl.lua:393]:sdk upload: not init
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>13 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>13 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>13 25 27</color>
[logic/model/CModel.lua:299]:3009没有这个动作idleCity
[logic/ui/CViewCtrl.lua:94]:CLimitRewardView ShowView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[logic/ui/CViewBase.lua:125]:CLimitRewardView LoadDone!
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 3
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
[core/table.lua:94]:CommonProcess-> = {
|  cb = "function: 0x6cb4e3e8"
|  json_result = true
|  timer = 67
}
break了
break了
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034577
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:49:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034587
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:49:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CItemTipsSkinView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsSkinView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034597
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:49:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034607
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:50:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CItemTipsSkinView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034617
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:50:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034627
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:50:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034637
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:50:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034647
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:50:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034657
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:50:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CNpcShopView ShowView
[core/global.lua:59]:<color=#ffeb04>渠道: </color>
[core/global.lua:59]:<color=#ffeb04>包名: ylq</color>
[logic/ui/CViewBase.lua:125]:CNpcShopView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CNpcShopView     CloseView
[logic/ui/CViewCtrl.lua:94]:CItemTipsSkinView ShowView
[logic/ui/CViewBase.lua:125]:CItemTipsSkinView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034667
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:51:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034677
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:51:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034687
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:51:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CItemTipsSkinView     CloseView
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034697
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:51:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034707
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:51:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034717
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:51:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034727
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:52:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034737
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:52:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034747
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:52:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034757
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:52:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034767
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:52:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034777
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:52:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034787
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:53:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034797
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:53:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034807
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:53:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034817
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:53:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034827
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:53:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034837
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:53:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034847
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:54:07
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CNpcShopView ShowView
[core/global.lua:59]:<color=#ffeb04>渠道: </color>
[core/global.lua:59]:<color=#ffeb04>包名: ylq</color>
[logic/ui/CViewBase.lua:125]:CNpcShopView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034857
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:54:17
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CNpcShopView     CloseView
[logic/ui/CViewCtrl.lua:104]:CLimitRewardView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034867
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:54:27
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034877
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:54:37
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034887
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:54:47
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4e164120"
|  |  AssociatedPick = "function: 0x4e164180"
|  |  AssociatedSubmit = "function: 0x4e164150"
|  |  CreateDefalutData = "function: 0x4e162100"
|  |  GetChaptetFubenData = "function: 0x4e163e40"
|  |  GetProgressThing = "function: 0x4e1641e0"
|  |  GetRemainTime = "function: 0x4e162098"
|  |  GetStatus = "function: 0x4e164240"
|  |  GetTaskClientExtStrDic = "function: 0x4e1641b0"
|  |  GetTaskTypeSpriteteName = "function: 0x4e163e10"
|  |  GetTraceInfo = "function: 0x4e163f68"
|  |  GetTraceNpcType = "function: 0x4e163de0"
|  |  GetValue = "function: 0x4e163ea8"
|  |  IsAbandon = "function: 0x4e163f08"
|  |  IsAddEscortDynamicNpc = "function: 0x4e164578"
|  |  IsMissMengTask = "function: 0x4e1620c8"
|  |  IsPassChaterFuben = "function: 0x4e163e70"
|  |  IsTaskSpecityAction = "function: 0x4e163f38"
|  |  IsTaskSpecityCategory = "function: 0x4e1640f0"
|  |  New = "function: 0x4e168a70"
|  |  NewByData = "function: 0x4e161578"
|  |  RaiseProgressIdx = "function: 0x4e164210"
|  |  RefreshTask = "function: 0x4e163ed8"
|  |  ResetEndTime = "function: 0x4e162168"
|  |  SetStatus = "function: 0x4e1645a8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4e162068"
|  }
|  m_CData = {
|  |  ChapterFb = "1,7"
|  |  autoDoNextTask = 10023
|  |  clientExtStr = ""
|  |  name = "野蛮人"
|  |  submitNpcId = 5005
|  |  submitRewardStr = {
|  |  |  [1] = "R1384"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,7"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 5005
|  |  autotype = 1
|  |  detaildesc = "看来教会的人对统帅部有着不可描叙的敌意！"
|  |  isdone = 0
|  |  name = "野蛮人"
|  |  patrolinfo = {}
|  |  pickiteminfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 5005
|  |  target = 5005
|  |  targetdesc = "教会与统帅部"
|  |  taskid = 10384
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8001
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickNpc = {
|  npcid = 72
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8001
}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcSay = {
|  fight = true
|  name = "遥"
|  npcid = 72
|  shape = 1011
|  text = "你也来祷告吗？"
}
[core/global.lua:59]:<color=#ffeb04> fight  true</color>
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 7005
}
[core/table.lua:94]:<--Net Send: task.C2GSAcceptTask = {
|  taskid = 10384
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 7005
}
[core/table.lua:94]:-->Net Receive: task.GS2CDialog = {
|  dialog = {
|  |  [1] = {
|  |  |  content = "难怪都说统帅部都是些无礼野蛮之徒，竟然连宠物都照顾不好，愿主宽恕!"
|  |  |  next = "2"
|  |  |  pre_id_list = "5005,0"
|  |  |  status = 2
|  |  |  subid = 1
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [2] = {
|  |  |  content = "我很抱歉，还有它不是我的宠物，你们对统帅部怕也有误解。"
|  |  |  next = "3"
|  |  |  pre_id_list = "5005,0"
|  |  |  status = 2
|  |  |  subid = 2
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [3] = {
|  |  |  content = "是吗？我还看到它特意停在路口等人，你们一定是故意来捣乱的。"
|  |  |  next = "4"
|  |  |  pre_id_list = "5005,0"
|  |  |  status = 2
|  |  |  subid = 3
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [4] = {
|  |  |  content = "在路口等我？真是奇怪，它到底是个什么东西……"
|  |  |  next = "5"
|  |  |  pre_id_list = "5005,0"
|  |  |  status = 2
|  |  |  subid = 4
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [5] = {
|  |  |  content = "总之，教会不欢迎你们这种野蛮的家伙!"
|  |  |  finish_event = "DONE"
|  |  |  next = "6"
|  |  |  pre_id_list = "5005,0"
|  |  |  status = 2
|  |  |  subid = 5
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  }
|  dialog_id = 10384
|  npc_id = 73
|  npc_name = "遥"
|  sessionidx = "488041"
|  shape = 1011
|  task_big_type = 2
|  task_small_type = 1
}
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshTask = {
|  accepttime = 1754034892
|  name = "野蛮人"
|  statusinfo = {
|  |  note = "Accept Task"
|  |  status = 1
|  }
|  target = 5005
|  taskid = 10384
}
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 3003
}
[core/table.lua:94]:<--Net Send: other.C2GSCallback = {
|  sessionidx = "488041"
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 3003
}
[core/table.lua:94]:-->Net Receive: task.GS2CDelTask = {
|  done = 1
|  taskid = 10384
}
[core/table.lua:94]:-->Net Receive: task.GS2CAddTask = {
|  taskdata = {
|  |  acceptnpc = 10603
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 312
|  |  |  |  }
|  |  |  |  name = "伊露"
|  |  |  |  npcid = 33560
|  |  |  |  npctype = 10603
|  |  |  |  pos_info = {
|  |  |  |  |  x = 30000
|  |  |  |  |  y = 12500
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33561
|  |  |  |  npctype = 10604
|  |  |  |  pos_info = {
|  |  |  |  |  x = 30000
|  |  |  |  |  y = 14000
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "虽然行动失败，但也要把事情结果告诉那位大人。"
|  |  name = "回复结果"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10603
|  |  target = 10603
|  |  targetdesc = "前往统帅部"
|  |  taskid = 10023
|  |  taskitem = {}
|  |  tasktype = 1
|  |  traceinfo = {}
|  |  type = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>add task </color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4e164120"
|  |  AssociatedPick = "function: 0x4e164180"
|  |  AssociatedSubmit = "function: 0x4e164150"
|  |  CreateDefalutData = "function: 0x4e162100"
|  |  GetChaptetFubenData = "function: 0x4e163e40"
|  |  GetProgressThing = "function: 0x4e1641e0"
|  |  GetRemainTime = "function: 0x4e162098"
|  |  GetStatus = "function: 0x4e164240"
|  |  GetTaskClientExtStrDic = "function: 0x4e1641b0"
|  |  GetTaskTypeSpriteteName = "function: 0x4e163e10"
|  |  GetTraceInfo = "function: 0x4e163f68"
|  |  GetTraceNpcType = "function: 0x4e163de0"
|  |  GetValue = "function: 0x4e163ea8"
|  |  IsAbandon = "function: 0x4e163f08"
|  |  IsAddEscortDynamicNpc = "function: 0x4e164578"
|  |  IsMissMengTask = "function: 0x4e1620c8"
|  |  IsPassChaterFuben = "function: 0x4e163e70"
|  |  IsTaskSpecityAction = "function: 0x4e163f38"
|  |  IsTaskSpecityCategory = "function: 0x4e1640f0"
|  |  New = "function: 0x4e168a70"
|  |  NewByData = "function: 0x4e161578"
|  |  RaiseProgressIdx = "function: 0x4e164210"
|  |  RefreshTask = "function: 0x4e163ed8"
|  |  ResetEndTime = "function: 0x4e162168"
|  |  SetStatus = "function: 0x4e1645a8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4e162068"
|  }
|  m_CData = {
|  |  ChapterFb = "1,7"
|  |  autoDoNextTask = 10025
|  |  clientExtStr = ""
|  |  name = "回复结果"
|  |  submitNpcId = 10603
|  |  submitRewardStr = {
|  |  |  [1] = "R1023"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10603
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 312
|  |  |  |  }
|  |  |  |  name = "伊露"
|  |  |  |  npcid = 33560
|  |  |  |  npctype = 10603
|  |  |  |  pos_info = {
|  |  |  |  |  x = 30000
|  |  |  |  |  y = 12500
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33561
|  |  |  |  npctype = 10604
|  |  |  |  pos_info = {
|  |  |  |  |  x = 30000
|  |  |  |  |  y = 14000
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "虽然行动失败，但也要把事情结果告诉那位大人。"
|  |  isdone = 0
|  |  name = "回复结果"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10603
|  |  target = 10603
|  |  targetdesc = "前往统帅部"
|  |  taskid = 10023
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 795250
|  |  exp = 20460
|  |  mask = "60"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 795250
|  exp = 20460
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 14500
|  |  mask = "80"
|  }
|  partnerid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 14500
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 9680
|  |  mask = "80"
|  }
|  partnerid = 2
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 9680
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  abnormal_attr_ratio = 400
|  |  attack = 360
|  |  critical_damage = 15000
|  |  critical_ratio = 600
|  |  cure_critical_ratio = 500
|  |  defense = 83
|  |  exp = 7100
|  |  grade = 8
|  |  hp = 3047
|  |  mask = "fffc0"
|  |  max_hp = 3047
|  |  power = 976
|  |  res_abnormal_ratio = 500
|  |  res_critical_ratio = 600
|  |  speed = 735
|  }
|  partnerid = 3
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  abnormal_attr_ratio = 400
|  attack = 360
|  critical_damage = 15000
|  critical_ratio = 600
|  cure_critical_ratio = 500
|  defense = 83
|  exp = 7100
|  grade = 8
|  hp = 3047
|  max_hp = 3047
|  power = 976
|  res_abnormal_ratio = 500
|  res_critical_ratio = 600
|  speed = 735
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 2100
|  |  mask = "80"
|  }
|  partnerid = 5
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 2100
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得#exp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#exp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#w1#G2500#n"
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 403
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 403
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 403
|  |  |  |  value = 8
|  |  |  }
|  |  }
|  |  id = 5
|  }
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G2500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B重华#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B重华#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B马面面#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B马面面#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B蛇姬#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B蛇姬#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B阿坊#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B阿坊#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034897
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:54:57
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4e164120"
|  |  AssociatedPick = "function: 0x4e164180"
|  |  AssociatedSubmit = "function: 0x4e164150"
|  |  CreateDefalutData = "function: 0x4e162100"
|  |  GetChaptetFubenData = "function: 0x4e163e40"
|  |  GetProgressThing = "function: 0x4e1641e0"
|  |  GetRemainTime = "function: 0x4e162098"
|  |  GetStatus = "function: 0x4e164240"
|  |  GetTaskClientExtStrDic = "function: 0x4e1641b0"
|  |  GetTaskTypeSpriteteName = "function: 0x4e163e10"
|  |  GetTraceInfo = "function: 0x4e163f68"
|  |  GetTraceNpcType = "function: 0x4e163de0"
|  |  GetValue = "function: 0x4e163ea8"
|  |  IsAbandon = "function: 0x4e163f08"
|  |  IsAddEscortDynamicNpc = "function: 0x4e164578"
|  |  IsMissMengTask = "function: 0x4e1620c8"
|  |  IsPassChaterFuben = "function: 0x4e163e70"
|  |  IsTaskSpecityAction = "function: 0x4e163f38"
|  |  IsTaskSpecityCategory = "function: 0x4e1640f0"
|  |  New = "function: 0x4e168a70"
|  |  NewByData = "function: 0x4e161578"
|  |  RaiseProgressIdx = "function: 0x4e164210"
|  |  RefreshTask = "function: 0x4e163ed8"
|  |  ResetEndTime = "function: 0x4e162168"
|  |  SetStatus = "function: 0x4e1645a8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4e162068"
|  }
|  m_CData = {
|  |  ChapterFb = "1,7"
|  |  autoDoNextTask = 10025
|  |  clientExtStr = ""
|  |  name = "回复结果"
|  |  submitNpcId = 10603
|  |  submitRewardStr = {
|  |  |  [1] = "R1023"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,7"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10603
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 312
|  |  |  |  }
|  |  |  |  name = "伊露"
|  |  |  |  npcid = 33560
|  |  |  |  npctype = 10603
|  |  |  |  pos_info = {
|  |  |  |  |  x = 30000
|  |  |  |  |  y = 12500
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33561
|  |  |  |  npctype = 10604
|  |  |  |  pos_info = {
|  |  |  |  |  x = 30000
|  |  |  |  |  y = 14000
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "虽然行动失败，但也要把事情结果告诉那位大人。"
|  |  isdone = 0
|  |  name = "回复结果"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10603
|  |  target = 10603
|  |  targetdesc = "前往统帅部"
|  |  taskid = 10023
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[logic/model/CModel.lua:299]:3009没有这个动作idleCity
[logic/ui/CViewCtrl.lua:94]:CMainMenuOperateView ShowView
[logic/ui/CViewBase.lua:125]:CMainMenuOperateView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 13001
}
[core/table.lua:94]:<--Net Send: achieve.C2GSAchieveMain = {}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 13001
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveMain = {
|  cur_point = 4
|  directions = {
|  |  [1] = {
|  |  |  cur = 4
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  id = 2
|  |  }
|  |  [3] = {
|  |  |  id = 3
|  |  }
|  |  [4] = {
|  |  |  id = 4
|  |  }
|  }
}
[logic/ui/CViewCtrl.lua:94]:CAchieveMainView ShowView
[logic/ui/CViewBase.lua:125]:CAchieveMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 13002
}
[core/table.lua:94]:<--Net Send: achieve.C2GSAchieveDirection = {
|  belong = 1
|  id = 1
}
[logic/ui/CViewCtrl.lua:104]:CMainMenuOperateView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 13002
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveDirection = {
|  achlist = {
|  |  [1] = {
|  |  |  cur = 7
|  |  |  id = 12402
|  |  }
|  |  [10] = {
|  |  |  cur = 21
|  |  |  id = 12503
|  |  }
|  |  [11] = {
|  |  |  cur = 21
|  |  |  id = 12504
|  |  }
|  |  [12] = {
|  |  |  cur = 963
|  |  |  id = 10201
|  |  }
|  |  [13] = {
|  |  |  cur = 963
|  |  |  id = 10202
|  |  }
|  |  [14] = {
|  |  |  cur = 963
|  |  |  id = 10203
|  |  }
|  |  [15] = {
|  |  |  cur = 21
|  |  |  id = 12508
|  |  }
|  |  [16] = {
|  |  |  cur = 21
|  |  |  id = 12509
|  |  }
|  |  [17] = {
|  |  |  cur = 963
|  |  |  id = 10206
|  |  }
|  |  [18] = {
|  |  |  cur = 963
|  |  |  id = 10207
|  |  }
|  |  [19] = {
|  |  |  cur = 963
|  |  |  id = 10208
|  |  }
|  |  [2] = {
|  |  |  cur = 963
|  |  |  id = 10205
|  |  }
|  |  [20] = {
|  |  |  cur = 963
|  |  |  id = 10209
|  |  }
|  |  [21] = {
|  |  |  cur = 963
|  |  |  id = 10210
|  |  }
|  |  [22] = {
|  |  |  cur = 963
|  |  |  id = 10211
|  |  }
|  |  [23] = {
|  |  |  cur = 7
|  |  |  id = 12404
|  |  }
|  |  [24] = {
|  |  |  cur = 7
|  |  |  id = 12405
|  |  }
|  |  [25] = {
|  |  |  cur = 21
|  |  |  id = 12506
|  |  }
|  |  [26] = {
|  |  |  cur = 13
|  |  |  id = 10109
|  |  }
|  |  [27] = {
|  |  |  cur = 963
|  |  |  id = 10204
|  |  }
|  |  [28] = {
|  |  |  cur = 13
|  |  |  id = 10102
|  |  }
|  |  [29] = {
|  |  |  cur = 7
|  |  |  id = 12401
|  |  }
|  |  [3] = {
|  |  |  cur = 7
|  |  |  id = 12409
|  |  }
|  |  [30] = {
|  |  |  cur = 7
|  |  |  id = 12408
|  |  }
|  |  [31] = {
|  |  |  cur = 7
|  |  |  id = 12407
|  |  }
|  |  [32] = {
|  |  |  cur = 10
|  |  |  done = 2
|  |  |  id = 10101
|  |  }
|  |  [33] = {
|  |  |  cur = 7
|  |  |  id = 12406
|  |  }
|  |  [34] = {
|  |  |  cur = 13
|  |  |  id = 10103
|  |  }
|  |  [35] = {
|  |  |  cur = 13
|  |  |  id = 10104
|  |  }
|  |  [36] = {
|  |  |  cur = 13
|  |  |  id = 10105
|  |  }
|  |  [37] = {
|  |  |  cur = 13
|  |  |  id = 10106
|  |  }
|  |  [38] = {
|  |  |  cur = 13
|  |  |  id = 10107
|  |  }
|  |  [39] = {
|  |  |  cur = 13
|  |  |  id = 10108
|  |  }
|  |  [4] = {
|  |  |  cur = 7
|  |  |  id = 12403
|  |  }
|  |  [5] = {
|  |  |  cur = 21
|  |  |  id = 12507
|  |  }
|  |  [6] = {
|  |  |  cur = 21
|  |  |  id = 12505
|  |  }
|  |  [7] = {
|  |  |  cur = 13
|  |  |  id = 10110
|  |  }
|  |  [8] = {
|  |  |  cur = 21
|  |  |  id = 12501
|  |  }
|  |  [9] = {
|  |  |  cur = 21
|  |  |  id = 12502
|  |  }
|  }
|  belong = 1
|  id = 1
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 13002
}
[core/table.lua:94]:<--Net Send: achieve.C2GSAchieveDirection = {
|  belong = 2
|  id = 1
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 13002
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveDirection = {
|  achlist = {
|  |  [1] = {
|  |  |  cur = 41000
|  |  |  id = 10401
|  |  }
|  |  [10] = {
|  |  |  cur = 41000
|  |  |  id = 10410
|  |  }
|  |  [11] = {
|  |  |  cur = 41000
|  |  |  id = 10411
|  |  }
|  |  [12] = {
|  |  |  cur = 41000
|  |  |  id = 10412
|  |  }
|  |  [13] = {
|  |  |  cur = 596280
|  |  |  done = 1
|  |  |  id = 10301
|  |  }
|  |  [14] = {
|  |  |  cur = 596280
|  |  |  done = 1
|  |  |  id = 10302
|  |  }
|  |  [15] = {
|  |  |  cur = 836250
|  |  |  id = 10303
|  |  }
|  |  [16] = {
|  |  |  cur = 836250
|  |  |  id = 10304
|  |  }
|  |  [17] = {
|  |  |  cur = 836250
|  |  |  id = 10305
|  |  }
|  |  [18] = {
|  |  |  cur = 836250
|  |  |  id = 10306
|  |  }
|  |  [19] = {
|  |  |  cur = 836250
|  |  |  id = 10307
|  |  }
|  |  [2] = {
|  |  |  cur = 41000
|  |  |  id = 10402
|  |  }
|  |  [20] = {
|  |  |  cur = 836250
|  |  |  id = 10308
|  |  }
|  |  [21] = {
|  |  |  cur = 836250
|  |  |  id = 10309
|  |  }
|  |  [22] = {
|  |  |  cur = 836250
|  |  |  id = 10310
|  |  }
|  |  [23] = {
|  |  |  cur = 836250
|  |  |  id = 10311
|  |  }
|  |  [24] = {
|  |  |  cur = 836250
|  |  |  id = 10312
|  |  }
|  |  [3] = {
|  |  |  cur = 41000
|  |  |  id = 10403
|  |  }
|  |  [4] = {
|  |  |  cur = 41000
|  |  |  id = 10404
|  |  }
|  |  [5] = {
|  |  |  cur = 41000
|  |  |  id = 10405
|  |  }
|  |  [6] = {
|  |  |  cur = 41000
|  |  |  id = 10406
|  |  }
|  |  [7] = {
|  |  |  cur = 41000
|  |  |  id = 10407
|  |  }
|  |  [8] = {
|  |  |  cur = 41000
|  |  |  id = 10408
|  |  }
|  |  [9] = {
|  |  |  cur = 41000
|  |  |  id = 10409
|  |  }
|  }
|  belong = 2
|  id = 1
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 13003
}
[core/table.lua:94]:<--Net Send: achieve.C2GSAchieveReward = {
|  id = 10301
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 13003
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveDirection = {
|  achlist = {
|  |  [1] = {
|  |  |  cur = 41000
|  |  |  id = 10401
|  |  }
|  |  [10] = {
|  |  |  cur = 41000
|  |  |  id = 10410
|  |  }
|  |  [11] = {
|  |  |  cur = 41000
|  |  |  id = 10411
|  |  }
|  |  [12] = {
|  |  |  cur = 41000
|  |  |  id = 10412
|  |  }
|  |  [13] = {
|  |  |  cur = 596280
|  |  |  done = 2
|  |  |  id = 10301
|  |  }
|  |  [14] = {
|  |  |  cur = 596280
|  |  |  done = 1
|  |  |  id = 10302
|  |  }
|  |  [15] = {
|  |  |  cur = 836250
|  |  |  id = 10303
|  |  }
|  |  [16] = {
|  |  |  cur = 836250
|  |  |  id = 10304
|  |  }
|  |  [17] = {
|  |  |  cur = 836250
|  |  |  id = 10305
|  |  }
|  |  [18] = {
|  |  |  cur = 836250
|  |  |  id = 10306
|  |  }
|  |  [19] = {
|  |  |  cur = 836250
|  |  |  id = 10307
|  |  }
|  |  [2] = {
|  |  |  cur = 41000
|  |  |  id = 10402
|  |  }
|  |  [20] = {
|  |  |  cur = 836250
|  |  |  id = 10308
|  |  }
|  |  [21] = {
|  |  |  cur = 836250
|  |  |  id = 10309
|  |  }
|  |  [22] = {
|  |  |  cur = 836250
|  |  |  id = 10310
|  |  }
|  |  [23] = {
|  |  |  cur = 836250
|  |  |  id = 10311
|  |  }
|  |  [24] = {
|  |  |  cur = 836250
|  |  |  id = 10312
|  |  }
|  |  [3] = {
|  |  |  cur = 41000
|  |  |  id = 10403
|  |  }
|  |  [4] = {
|  |  |  cur = 41000
|  |  |  id = 10404
|  |  }
|  |  [5] = {
|  |  |  cur = 41000
|  |  |  id = 10405
|  |  }
|  |  [6] = {
|  |  |  cur = 41000
|  |  |  id = 10406
|  |  }
|  |  [7] = {
|  |  |  cur = 41000
|  |  |  id = 10407
|  |  }
|  |  [8] = {
|  |  |  cur = 41000
|  |  |  id = 10408
|  |  }
|  |  [9] = {
|  |  |  cur = 41000
|  |  |  id = 10409
|  |  }
|  }
|  belong = 2
|  id = 1
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 799250
|  |  mask = "20"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 799250
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 2
|  |  |  |  [2] = 3
|  |  |  }
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveMain = {
|  cur_point = 9
|  directions = {
|  |  [1] = {
|  |  |  cur = 9
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  id = 2
|  |  }
|  |  [3] = {
|  |  |  id = 3
|  |  }
|  |  [4] = {
|  |  |  id = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#w1#G4000#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G4000#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshAchieveTask = {
|  info = {
|  |  achievetype = 2
|  |  degree = 1
|  |  describe = "领取1次成就奖励"
|  |  name = "成就奖励"
|  |  target = 1
|  |  taskid = 31510
|  }
}
[core/global.lua:59]:<color=#ffeb04>刷新任务 >>> 不存在任务ID: 31510</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 13003
}
[core/table.lua:94]:<--Net Send: achieve.C2GSAchieveReward = {
|  id = 10302
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 13003
}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034908
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:55:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveDirection = {
|  achlist = {
|  |  [1] = {
|  |  |  cur = 41000
|  |  |  id = 10401
|  |  }
|  |  [10] = {
|  |  |  cur = 41000
|  |  |  id = 10410
|  |  }
|  |  [11] = {
|  |  |  cur = 41000
|  |  |  id = 10411
|  |  }
|  |  [12] = {
|  |  |  cur = 41000
|  |  |  id = 10412
|  |  }
|  |  [13] = {
|  |  |  cur = 596280
|  |  |  done = 2
|  |  |  id = 10301
|  |  }
|  |  [14] = {
|  |  |  cur = 596280
|  |  |  done = 2
|  |  |  id = 10302
|  |  }
|  |  [15] = {
|  |  |  cur = 840250
|  |  |  id = 10303
|  |  }
|  |  [16] = {
|  |  |  cur = 840250
|  |  |  id = 10304
|  |  }
|  |  [17] = {
|  |  |  cur = 840250
|  |  |  id = 10305
|  |  }
|  |  [18] = {
|  |  |  cur = 840250
|  |  |  id = 10306
|  |  }
|  |  [19] = {
|  |  |  cur = 840250
|  |  |  id = 10307
|  |  }
|  |  [2] = {
|  |  |  cur = 41000
|  |  |  id = 10402
|  |  }
|  |  [20] = {
|  |  |  cur = 840250
|  |  |  id = 10308
|  |  }
|  |  [21] = {
|  |  |  cur = 840250
|  |  |  id = 10309
|  |  }
|  |  [22] = {
|  |  |  cur = 840250
|  |  |  id = 10310
|  |  }
|  |  [23] = {
|  |  |  cur = 840250
|  |  |  id = 10311
|  |  }
|  |  [24] = {
|  |  |  cur = 840250
|  |  |  id = 10312
|  |  }
|  |  [3] = {
|  |  |  cur = 41000
|  |  |  id = 10403
|  |  }
|  |  [4] = {
|  |  |  cur = 41000
|  |  |  id = 10404
|  |  }
|  |  [5] = {
|  |  |  cur = 41000
|  |  |  id = 10405
|  |  }
|  |  [6] = {
|  |  |  cur = 41000
|  |  |  id = 10406
|  |  }
|  |  [7] = {
|  |  |  cur = 41000
|  |  |  id = 10407
|  |  }
|  |  [8] = {
|  |  |  cur = 41000
|  |  |  id = 10408
|  |  }
|  |  [9] = {
|  |  |  cur = 41000
|  |  |  id = 10409
|  |  }
|  }
|  belong = 2
|  id = 1
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 3
|  |  |  }
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 806250
|  |  mask = "20"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 806250
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveMain = {
|  cur_point = 18
|  directions = {
|  |  [1] = {
|  |  |  cur = 18
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  id = 2
|  |  }
|  |  [3] = {
|  |  |  id = 3
|  |  }
|  |  [4] = {
|  |  |  id = 4
|  |  }
|  }
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#w1#G7000#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G7000#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshAchieveTask = {
|  info = {
|  |  achievetype = 2
|  |  degree = 1
|  |  describe = "领取1次成就奖励"
|  |  name = "成就奖励"
|  |  target = 1
|  |  taskid = 31510
|  }
}
[core/global.lua:59]:<color=#ffeb04>刷新任务 >>> 不存在任务ID: 31510</color>
[core/table.lua:94]:<--Net Send: achieve.C2GSCloseMainUI = {}
[logic/ui/CViewCtrl.lua:104]:CAchieveMainView     CloseView
[logic/ui/CViewCtrl.lua:94]:CMainMenuOperateView ShowView
[logic/ui/CViewBase.lua:125]:CMainMenuOperateView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CMainMenuOperateView     CloseView
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034918
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:55:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034928
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:55:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034938
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:55:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4e164120"
|  |  AssociatedPick = "function: 0x4e164180"
|  |  AssociatedSubmit = "function: 0x4e164150"
|  |  CreateDefalutData = "function: 0x4e162100"
|  |  GetChaptetFubenData = "function: 0x4e163e40"
|  |  GetProgressThing = "function: 0x4e1641e0"
|  |  GetRemainTime = "function: 0x4e162098"
|  |  GetStatus = "function: 0x4e164240"
|  |  GetTaskClientExtStrDic = "function: 0x4e1641b0"
|  |  GetTaskTypeSpriteteName = "function: 0x4e163e10"
|  |  GetTraceInfo = "function: 0x4e163f68"
|  |  GetTraceNpcType = "function: 0x4e163de0"
|  |  GetValue = "function: 0x4e163ea8"
|  |  IsAbandon = "function: 0x4e163f08"
|  |  IsAddEscortDynamicNpc = "function: 0x4e164578"
|  |  IsMissMengTask = "function: 0x4e1620c8"
|  |  IsPassChaterFuben = "function: 0x4e163e70"
|  |  IsTaskSpecityAction = "function: 0x4e163f38"
|  |  IsTaskSpecityCategory = "function: 0x4e1640f0"
|  |  New = "function: 0x4e168a70"
|  |  NewByData = "function: 0x4e161578"
|  |  RaiseProgressIdx = "function: 0x4e164210"
|  |  RefreshTask = "function: 0x4e163ed8"
|  |  ResetEndTime = "function: 0x4e162168"
|  |  SetStatus = "function: 0x4e1645a8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4e162068"
|  }
|  m_CData = {
|  |  ChapterFb = "1,7"
|  |  autoDoNextTask = 10025
|  |  clientExtStr = ""
|  |  name = "回复结果"
|  |  submitNpcId = 10603
|  |  submitRewardStr = {
|  |  |  [1] = "R1023"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,7"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10603
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 312
|  |  |  |  }
|  |  |  |  name = "伊露"
|  |  |  |  npcid = 33560
|  |  |  |  npctype = 10603
|  |  |  |  pos_info = {
|  |  |  |  |  x = 30000
|  |  |  |  |  y = 12500
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33561
|  |  |  |  npctype = 10604
|  |  |  |  pos_info = {
|  |  |  |  |  x = 30000
|  |  |  |  |  y = 14000
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "虽然行动失败，但也要把事情结果告诉那位大人。"
|  |  isdone = 0
|  |  name = "回复结果"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10603
|  |  target = 10603
|  |  targetdesc = "前往统帅部"
|  |  taskid = 10023
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1151
|  |  |  }
|  |  |  name = "邓酒爷"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 202
|  |  npctype = 5011
|  }
|  eid = 19
|  scene_id = 12
|  type = 2
}
[logic/model/CModel.lua:299]:3009没有这个动作idleCity
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 19
|  pos_info = {
|  |  x = 22300
|  |  y = 5100
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1151
|  }
|  name = "邓酒爷"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1503
|  |  |  }
|  |  |  name = "喵小布"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 21
|  |  npctype = 5047
|  }
|  eid = 6
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 6
|  pos_info = {
|  |  x = 33000
|  |  y = 9500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1503
|  }
|  name = "喵小布"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10100
|  |  triggerNpc = 5041
|  }
|  [2] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点13(大)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30781
|  |  npctype = 13
|  |  owner = "领主: "
|  }
|  eid = 23
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 23
|  pos_info = {
|  |  x = 37000
|  |  y = 8000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点13(大)"
|  owner = "领主: "
|  trapmine = {}
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10100</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小叶 1509 [40,14.6,0] 172 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [46.8,10.2,0] -160</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  诚哥 1150 [46.3,9,0] 7 false</color>
[logic/model/CModel.lua:299]:3009没有这个动作idleCity
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034948
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:55:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/model/CModel.lua:299]:3009没有这个动作idleCity
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点1(小)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30757
|  |  npctype = 1
|  |  owner = "领主: "
|  }
|  eid = 32
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 32
|  pos_info = {
|  |  x = 11000
|  |  y = 23000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点1(小)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1150
|  |  |  }
|  |  |  name = "飞龙哥"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 199
|  |  npctype = 5010
|  }
|  eid = 18
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 18
|  pos_info = {
|  |  x = 29000
|  |  y = 3500
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1150
|  }
|  name = "飞龙哥"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点4(小)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30763
|  |  npctype = 4
|  |  owner = "领主: "
|  }
|  eid = 35
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 35
|  pos_info = {
|  |  x = 15000
|  |  y = 11000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点4(小)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小佳"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 11
|  |  npctype = 5043
|  }
|  eid = 4
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 4
|  pos_info = {
|  |  x = 27000
|  |  y = 4000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小佳"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小兰"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 14
|  |  npctype = 5044
|  }
|  eid = 5
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 5
|  pos_info = {
|  |  x = 19000
|  |  y = 4000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小兰"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点7(大)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30769
|  |  npctype = 7
|  |  owner = "领主: "
|  }
|  eid = 38
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 38
|  pos_info = {
|  |  x = 23000
|  |  y = 4000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点7(大)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点8(小)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30771
|  |  npctype = 8
|  |  owner = "领主: "
|  }
|  eid = 39
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 39
|  pos_info = {
|  |  x = 31000
|  |  y = 3000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点8(小)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点14(小)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30783
|  |  npctype = 14
|  |  owner = "领主: "
|  }
|  eid = 24
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 24
|  pos_info = {
|  |  x = 44000
|  |  y = 4000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点14(小)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 26
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 42
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1014
|  |  |  }
|  |  |  name = "乔焱"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 66
|  |  npctype = 5003
|  }
|  eid = 11
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 11
|  pos_info = {
|  |  x = 13200
|  |  y = 24600
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1014
|  }
|  name = "乔焱"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 12
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点5(大)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30765
|  |  npctype = 5
|  |  owner = "领主: "
|  }
|  eid = 36
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 36
|  pos_info = {
|  |  x = 16000
|  |  y = 19000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点5(大)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1510
|  |  |  }
|  |  |  name = "小佳"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 57
|  |  npctype = 5064
|  }
|  eid = 8
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 8
|  pos_info = {
|  |  x = 13800
|  |  y = 20000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1510
|  }
|  name = "小佳"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点3(小)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30761
|  |  npctype = 3
|  |  owner = "领主: "
|  }
|  eid = 34
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 34
|  pos_info = {
|  |  x = 15000
|  |  y = 1000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点3(小)"
|  owner = "领主: "
|  trapmine = {}
}
[core/global.lua:59]:<color=#ffeb04> Trigger............  33560</color>
[core/table.lua:94]:Table = {
|  map_id = 101000
|  model_info = {
|  |  scale = 1
|  |  shape = 312
|  }
|  name = "伊露"
|  npcid = 33560
|  npctype = 10603
|  pos_info = {
|  |  x = 30
|  |  y = 12.5
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8001
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickNpc = {
|  npcid = 33560
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8001
}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcSay = {
|  name = "伊露"
|  npcid = 33560
|  shape = 312
|  text = "说多少次，别当我是小孩子！"
}
[core/global.lua:59]:<color=#ffeb04> fight  false</color>
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 7005
}
[core/table.lua:94]:<--Net Send: task.C2GSAcceptTask = {
|  taskid = 10023
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 7005
}
[core/table.lua:94]:-->Net Receive: task.GS2CDialog = {
|  dialog = {
|  |  [1] = {
|  |  |  content = "故意放走实验材料，难道你是敌人派来的间谍！"
|  |  |  next = "2"
|  |  |  pre_id_list = "10603,0"
|  |  |  status = 2
|  |  |  subid = 1
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [2] = {
|  |  |  content = "难不成说那么久都白解释了……"
|  |  |  next = "3"
|  |  |  pre_id_list = "10603,0"
|  |  |  status = 2
|  |  |  subid = 2
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [3] = {
|  |  |  content = "我要以执行官的名义将你逮捕！"
|  |  |  next = "4"
|  |  |  pre_id_list = "10603,0"
|  |  |  status = 2
|  |  |  subid = 3
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [4] = {
|  |  |  content = "哎呀呀，发生什么事这么热闹？"
|  |  |  finish_event = "DONE"
|  |  |  next = "0"
|  |  |  pre_id_list = "10603,10604"
|  |  |  status = 2
|  |  |  subid = 4
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  }
|  dialog_id = 10023
|  npc_id = 33560
|  npc_name = "伊露"
|  sessionidx = "489041"
|  shape = 312
|  task_big_type = 2
|  task_small_type = 1
}
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshTask = {
|  accepttime = 1754034951
|  name = "回复结果"
|  statusinfo = {
|  |  note = "Accept Task"
|  |  status = 1
|  }
|  target = 10603
|  taskid = 10023
}
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我恨你！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不要恨我。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我们分手吧</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034958
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:55:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 好。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [44,26,0] 60</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 其实我喜欢男生</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 shengqi true</color>
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034968
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:56:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  2 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034978
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:56:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034988
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:56:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754034998
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:56:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035008
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:56:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 3</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 3003
}
[core/table.lua:94]:<--Net Send: other.C2GSCallback = {
|  sessionidx = "489041"
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 3003
}
[core/table.lua:94]:-->Net Receive: task.GS2CDelTask = {
|  done = 1
|  taskid = 10023
}
[core/table.lua:94]:-->Net Receive: task.GS2CAddTask = {
|  taskdata = {
|  |  acceptnpc = 10606
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 312
|  |  |  |  }
|  |  |  |  name = "伊露"
|  |  |  |  npcid = 33562
|  |  |  |  npctype = 10605
|  |  |  |  pos_info = {
|  |  |  |  |  x = 30000
|  |  |  |  |  y = 12500
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33563
|  |  |  |  npctype = 10606
|  |  |  |  pos_info = {
|  |  |  |  |  x = 30000
|  |  |  |  |  y = 14000
|  |  |  |  }
|  |  |  }
|  |  |  [3] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 308
|  |  |  |  }
|  |  |  |  name = "黑烈"
|  |  |  |  npcid = 33564
|  |  |  |  npctype = 10607
|  |  |  |  pos_info = {
|  |  |  |  |  x = 28000
|  |  |  |  |  y = 12300
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "新执行官登场！"
|  |  name = "执行官"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10606
|  |  target = 10606
|  |  targetdesc = "白"
|  |  taskid = 10024
|  |  taskitem = {}
|  |  tasktype = 1
|  |  traceinfo = {}
|  |  type = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>add task </color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4e164120"
|  |  AssociatedPick = "function: 0x4e164180"
|  |  AssociatedSubmit = "function: 0x4e164150"
|  |  CreateDefalutData = "function: 0x4e162100"
|  |  GetChaptetFubenData = "function: 0x4e163e40"
|  |  GetProgressThing = "function: 0x4e1641e0"
|  |  GetRemainTime = "function: 0x4e162098"
|  |  GetStatus = "function: 0x4e164240"
|  |  GetTaskClientExtStrDic = "function: 0x4e1641b0"
|  |  GetTaskTypeSpriteteName = "function: 0x4e163e10"
|  |  GetTraceInfo = "function: 0x4e163f68"
|  |  GetTraceNpcType = "function: 0x4e163de0"
|  |  GetValue = "function: 0x4e163ea8"
|  |  IsAbandon = "function: 0x4e163f08"
|  |  IsAddEscortDynamicNpc = "function: 0x4e164578"
|  |  IsMissMengTask = "function: 0x4e1620c8"
|  |  IsPassChaterFuben = "function: 0x4e163e70"
|  |  IsTaskSpecityAction = "function: 0x4e163f38"
|  |  IsTaskSpecityCategory = "function: 0x4e1640f0"
|  |  New = "function: 0x4e168a70"
|  |  NewByData = "function: 0x4e161578"
|  |  RaiseProgressIdx = "function: 0x4e164210"
|  |  RefreshTask = "function: 0x4e163ed8"
|  |  ResetEndTime = "function: 0x4e162168"
|  |  SetStatus = "function: 0x4e1645a8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4e162068"
|  }
|  m_CData = {
|  |  ChapterFb = "1,7"
|  |  autoDoNextTask = 0
|  |  clientExtStr = ""
|  |  name = "执行官"
|  |  submitNpcId = 10606
|  |  submitRewardStr = {
|  |  |  [1] = "R1024"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10606
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 312
|  |  |  |  }
|  |  |  |  name = "伊露"
|  |  |  |  npcid = 33562
|  |  |  |  npctype = 10605
|  |  |  |  pos_info = {
|  |  |  |  |  x = 30000
|  |  |  |  |  y = 12500
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33563
|  |  |  |  npctype = 10606
|  |  |  |  pos_info = {
|  |  |  |  |  x = 30000
|  |  |  |  |  y = 14000
|  |  |  |  }
|  |  |  }
|  |  |  [3] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 308
|  |  |  |  }
|  |  |  |  name = "黑烈"
|  |  |  |  npcid = 33564
|  |  |  |  npctype = 10607
|  |  |  |  pos_info = {
|  |  |  |  |  x = 28000
|  |  |  |  |  y = 12300
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "新执行官登场！"
|  |  isdone = 0
|  |  name = "执行官"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10606
|  |  target = 10606
|  |  targetdesc = "白"
|  |  taskid = 10024
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 808750
|  |  exp = 20960
|  |  mask = "60"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 808750
|  exp = 20960
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得#exp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#exp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 302
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 302
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 302
|  |  |  |  value = 13
|  |  |  }
|  |  }
|  |  id = 4
|  }
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#w1#G2500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G2500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B重华#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 501
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 501
|  |  |  |  value = 1
|  |  |  }
|  |  |  [5] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 502
|  |  |  |  value = 10
|  |  |  }
|  |  |  [6] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 501
|  |  |  |  value = 3
|  |  |  }
|  |  }
|  |  id = 1
|  }
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B重华#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B马面面#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B马面面#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B蛇姬#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B蛇姬#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B阿坊#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B阿坊#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  abnormal_attr_ratio = 400
|  |  attack = 493
|  |  critical_damage = 15000
|  |  critical_ratio = 800
|  |  cure_critical_ratio = 500
|  |  defense = 103
|  |  exp = 15000
|  |  grade = 13
|  |  hp = 3543
|  |  mask = "fffc0"
|  |  max_hp = 3543
|  |  power = 1230
|  |  res_abnormal_ratio = 500
|  |  res_critical_ratio = 600
|  |  speed = 420
|  }
|  partnerid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  abnormal_attr_ratio = 400
|  attack = 493
|  critical_damage = 15000
|  critical_ratio = 800
|  cure_critical_ratio = 500
|  defense = 103
|  exp = 15000
|  grade = 13
|  hp = 3543
|  max_hp = 3543
|  power = 1230
|  res_abnormal_ratio = 500
|  res_critical_ratio = 600
|  speed = 420
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 501
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 501
|  |  |  |  value = 1
|  |  |  }
|  |  |  [5] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 502
|  |  |  |  value = 10
|  |  |  }
|  |  |  [6] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 501
|  |  |  |  value = 4
|  |  |  }
|  |  }
|  |  id = 1
|  }
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  abnormal_attr_ratio = 400
|  |  attack = 382
|  |  critical_damage = 15000
|  |  critical_ratio = 800
|  |  cure_critical_ratio = 500
|  |  defense = 89
|  |  exp = 10180
|  |  grade = 10
|  |  hp = 3021
|  |  mask = "fffc0"
|  |  max_hp = 3021
|  |  power = 1007
|  |  res_abnormal_ratio = 500
|  |  res_critical_ratio = 500
|  |  speed = 315
|  }
|  partnerid = 2
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  abnormal_attr_ratio = 400
|  attack = 382
|  critical_damage = 15000
|  critical_ratio = 800
|  cure_critical_ratio = 500
|  defense = 89
|  exp = 10180
|  grade = 10
|  hp = 3021
|  max_hp = 3021
|  power = 1007
|  res_abnormal_ratio = 500
|  res_critical_ratio = 500
|  speed = 315
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 7600
|  |  mask = "80"
|  }
|  partnerid = 3
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 7600
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  abnormal_attr_ratio = 400
|  |  attack = 328
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 59
|  |  exp = 2600
|  |  grade = 4
|  |  hp = 2154
|  |  mask = "fffc0"
|  |  max_hp = 2154
|  |  power = 774
|  |  res_abnormal_ratio = 500
|  |  res_critical_ratio = 400
|  |  speed = 70
|  }
|  partnerid = 5
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  abnormal_attr_ratio = 400
|  attack = 328
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 59
|  exp = 2600
|  grade = 4
|  hp = 2154
|  max_hp = 2154
|  power = 774
|  res_abnormal_ratio = 500
|  res_critical_ratio = 400
|  speed = 70
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035018
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:56:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035028
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:57:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10100</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [5] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035038
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:57:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4e164120"
|  |  AssociatedPick = "function: 0x4e164180"
|  |  AssociatedSubmit = "function: 0x4e164150"
|  |  CreateDefalutData = "function: 0x4e162100"
|  |  GetChaptetFubenData = "function: 0x4e163e40"
|  |  GetProgressThing = "function: 0x4e1641e0"
|  |  GetRemainTime = "function: 0x4e162098"
|  |  GetStatus = "function: 0x4e164240"
|  |  GetTaskClientExtStrDic = "function: 0x4e1641b0"
|  |  GetTaskTypeSpriteteName = "function: 0x4e163e10"
|  |  GetTraceInfo = "function: 0x4e163f68"
|  |  GetTraceNpcType = "function: 0x4e163de0"
|  |  GetValue = "function: 0x4e163ea8"
|  |  IsAbandon = "function: 0x4e163f08"
|  |  IsAddEscortDynamicNpc = "function: 0x4e164578"
|  |  IsMissMengTask = "function: 0x4e1620c8"
|  |  IsPassChaterFuben = "function: 0x4e163e70"
|  |  IsTaskSpecityAction = "function: 0x4e163f38"
|  |  IsTaskSpecityCategory = "function: 0x4e1640f0"
|  |  New = "function: 0x4e168a70"
|  |  NewByData = "function: 0x4e161578"
|  |  RaiseProgressIdx = "function: 0x4e164210"
|  |  RefreshTask = "function: 0x4e163ed8"
|  |  ResetEndTime = "function: 0x4e162168"
|  |  SetStatus = "function: 0x4e1645a8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4e162068"
|  }
|  m_CData = {
|  |  ChapterFb = "1,7"
|  |  autoDoNextTask = 0
|  |  clientExtStr = ""
|  |  name = "执行官"
|  |  submitNpcId = 10606
|  |  submitRewardStr = {
|  |  |  [1] = "R1024"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,7"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10606
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 312
|  |  |  |  }
|  |  |  |  name = "伊露"
|  |  |  |  npcid = 33562
|  |  |  |  npctype = 10605
|  |  |  |  pos_info = {
|  |  |  |  |  x = 30000
|  |  |  |  |  y = 12500
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33563
|  |  |  |  npctype = 10606
|  |  |  |  pos_info = {
|  |  |  |  |  x = 30000
|  |  |  |  |  y = 14000
|  |  |  |  }
|  |  |  }
|  |  |  [3] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 308
|  |  |  |  }
|  |  |  |  name = "黑烈"
|  |  |  |  npcid = 33564
|  |  |  |  npctype = 10607
|  |  |  |  pos_info = {
|  |  |  |  |  x = 28000
|  |  |  |  |  y = 12300
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "新执行官登场！"
|  |  isdone = 0
|  |  name = "执行官"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10606
|  |  target = 10606
|  |  targetdesc = "白"
|  |  taskid = 10024
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/global.lua:59]:<color=#ffeb04> Trigger............  33563</color>
[core/table.lua:94]:Table = {
|  map_id = 101000
|  model_info = {
|  |  scale = 1
|  |  shape = 801
|  }
|  name = "白"
|  npcid = 33563
|  npctype = 10606
|  pos_info = {
|  |  x = 30
|  |  y = 14
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8001
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickNpc = {
|  npcid = 33563
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8001
}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcSay = {
|  name = "白"
|  npcid = 33563
|  shape = 801
|  text = "哎呀哎呀~你从哪里来呀？喜欢什么？我是在关心你呀~~"
}
[core/global.lua:59]:<color=#ffeb04> fight  false</color>
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 7005
}
[core/table.lua:94]:<--Net Send: task.C2GSAcceptTask = {
|  taskid = 10024
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 7005
}
[core/table.lua:94]:-->Net Receive: task.GS2CDialog = {
|  dialog = {
|  |  [1] = {
|  |  |  content = "白大人，实验材料被放走，我要逮捕他进行审问。"
|  |  |  next = "2"
|  |  |  pre_id_list = "10605,0"
|  |  |  status = 2
|  |  |  subid = 1
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [2] = {
|  |  |  content = "我只是路过，没有帮到忙也不用被逮捕吧……"
|  |  |  next = "3"
|  |  |  pre_id_list = "10605,0"
|  |  |  status = 2
|  |  |  subid = 2
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [3] = {
|  |  |  content = "嗯？不是才提交了报名登记，怎么又来了。"
|  |  |  next = "4"
|  |  |  pre_id_list = "10607,0"
|  |  |  status = 2
|  |  |  subid = 3
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [4] = {
|  |  |  content = "你们认识？"
|  |  |  next = "5"
|  |  |  pre_id_list = "0,10606"
|  |  |  status = 2
|  |  |  subid = 4
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [5] = {
|  |  |  content = "不，这位就是刚刚我跟你们说的那个人。"
|  |  |  next = "6"
|  |  |  pre_id_list = "10607,0"
|  |  |  status = 2
|  |  |  subid = 5
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [6] = {
|  |  |  content = "原来是这样，伊露看你误会了吧。"
|  |  |  next = "7"
|  |  |  pre_id_list = "0,10606"
|  |  |  status = 2
|  |  |  subid = 6
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [7] = {
|  |  |  content = "哼，一切都是这个人的错！"
|  |  |  finish_event = "DONE"
|  |  |  next = "0"
|  |  |  pre_id_list = "10605,0"
|  |  |  status = 2
|  |  |  subid = 7
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  }
|  dialog_id = 10024
|  npc_id = 33563
|  npc_name = "白"
|  sessionidx = "490041"
|  shape = 801
|  task_big_type = 2
|  task_small_type = 1
}
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshTask = {
|  accepttime = 1754035040
|  name = "执行官"
|  statusinfo = {
|  |  note = "Accept Task"
|  |  status = 1
|  }
|  target = 10606
|  taskid = 10024
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveDone = {
|  id = 10201
|  pop = true
}
[logic/ui/CViewCtrl.lua:94]:CAchieveFinishTipsView ShowView
[core/table.lua:94]:-->Net Receive: achieve.GS2CAchieveRedDot = {
|  infolist = {
|  |  [1] = {
|  |  |  blist = {
|  |  |  |  [1] = 1
|  |  |  |  [2] = 3
|  |  |  }
|  |  |  id = 1
|  |  }
|  |  [2] = {
|  |  |  blist = {
|  |  |  |  [1] = 8
|  |  |  }
|  |  |  id = 4
|  |  }
|  }
}
[logic/ui/CViewBase.lua:125]:CAchieveFinishTipsView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035048
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:57:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CAchieveFinishTipsView     CloseView
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035058
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:57:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035068
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:57:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035078
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:57:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035088
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:58:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035098
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:58:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035108
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:58:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035118
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:58:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035128
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:58:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035138
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:58:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 3</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 3003
}
[core/table.lua:94]:<--Net Send: other.C2GSCallback = {
|  sessionidx = "490041"
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 3003
}
[core/table.lua:94]:-->Net Receive: task.GS2CDelTask = {
|  done = 1
|  taskid = 10024
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得#exp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#exp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 15500
|  |  mask = "80"
|  }
|  partnerid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 15500
}
[core/table.lua:94]:-->Net Receive: task.GS2CAddTask = {
|  taskdata = {
|  |  acceptnpc = 11437
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 312
|  |  |  |  }
|  |  |  |  name = "伊露"
|  |  |  |  npcid = 33565
|  |  |  |  npctype = 11437
|  |  |  |  pos_info = {
|  |  |  |  |  x = 29600
|  |  |  |  |  y = 12700
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33566
|  |  |  |  npctype = 11438
|  |  |  |  pos_info = {
|  |  |  |  |  x = 6000
|  |  |  |  |  y = 24700
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "抓住到处乱窜的嘟噜噜。"
|  |  name = "协助调查"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 11438
|  |  target = 11437
|  |  targetdesc = "跟上伊露"
|  |  taskid = 10385
|  |  taskitem = {}
|  |  tasktype = 9
|  |  traceinfo = {
|  |  |  mapid = 101000
|  |  |  npctype = 11437
|  |  |  pos_x = 5600
|  |  |  pos_y = 23700
|  |  }
|  |  type = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>add task </color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4e164120"
|  |  AssociatedPick = "function: 0x4e164180"
|  |  AssociatedSubmit = "function: 0x4e164150"
|  |  CreateDefalutData = "function: 0x4e162100"
|  |  GetChaptetFubenData = "function: 0x4e163e40"
|  |  GetProgressThing = "function: 0x4e1641e0"
|  |  GetRemainTime = "function: 0x4e162098"
|  |  GetStatus = "function: 0x4e164240"
|  |  GetTaskClientExtStrDic = "function: 0x4e1641b0"
|  |  GetTaskTypeSpriteteName = "function: 0x4e163e10"
|  |  GetTraceInfo = "function: 0x4e163f68"
|  |  GetTraceNpcType = "function: 0x4e163de0"
|  |  GetValue = "function: 0x4e163ea8"
|  |  IsAbandon = "function: 0x4e163f08"
|  |  IsAddEscortDynamicNpc = "function: 0x4e164578"
|  |  IsMissMengTask = "function: 0x4e1620c8"
|  |  IsPassChaterFuben = "function: 0x4e163e70"
|  |  IsTaskSpecityAction = "function: 0x4e163f38"
|  |  IsTaskSpecityCategory = "function: 0x4e1640f0"
|  |  New = "function: 0x4e168a70"
|  |  NewByData = "function: 0x4e161578"
|  |  RaiseProgressIdx = "function: 0x4e164210"
|  |  RefreshTask = "function: 0x4e163ed8"
|  |  ResetEndTime = "function: 0x4e162168"
|  |  SetStatus = "function: 0x4e1645a8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4e162068"
|  }
|  m_CData = {
|  |  ChapterFb = "1,7"
|  |  autoDoNextTask = 10025
|  |  clientExtStr = ""
|  |  name = "协助调查"
|  |  submitNpcId = 11438
|  |  submitRewardStr = {
|  |  |  [1] = "R1385"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 11437
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 312
|  |  |  |  }
|  |  |  |  name = "伊露"
|  |  |  |  npcid = 33565
|  |  |  |  npctype = 11437
|  |  |  |  pos_info = {
|  |  |  |  |  x = 29600
|  |  |  |  |  y = 12700
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33566
|  |  |  |  npctype = 11438
|  |  |  |  pos_info = {
|  |  |  |  |  x = 6000
|  |  |  |  |  y = 24700
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "抓住到处乱窜的嘟噜噜。"
|  |  isdone = 0
|  |  name = "协助调查"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 11438
|  |  target = 11437
|  |  targetdesc = "跟上伊露"
|  |  taskid = 10385
|  |  taskitem = {}
|  |  tasktype = 9
|  |  time = 0
|  |  traceinfo = {
|  |  |  mapid = 101000
|  |  |  npctype = 11437
|  |  |  pos_x = 5600
|  |  |  pos_y = 23700
|  |  }
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#w1#G2500#n"
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 10680
|  |  mask = "80"
|  }
|  partnerid = 2
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 10680
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G2500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 8100
|  |  mask = "80"
|  }
|  partnerid = 3
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 8100
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B重华#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 811250
|  |  exp = 21460
|  |  mask = "60"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 811250
|  exp = 21460
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B重华#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 3100
|  |  mask = "80"
|  }
|  partnerid = 5
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 3100
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B马面面#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B马面面#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B蛇姬#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B蛇姬#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B阿坊#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B阿坊#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:Table = {
|  mapid = 101000
|  npctype = 11437
|  pos_x = 5600
|  pos_y = 23700
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035148
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:59:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035158
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:59:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035168
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:59:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4e164120"
|  |  AssociatedPick = "function: 0x4e164180"
|  |  AssociatedSubmit = "function: 0x4e164150"
|  |  CreateDefalutData = "function: 0x4e162100"
|  |  GetChaptetFubenData = "function: 0x4e163e40"
|  |  GetProgressThing = "function: 0x4e1641e0"
|  |  GetRemainTime = "function: 0x4e162098"
|  |  GetStatus = "function: 0x4e164240"
|  |  GetTaskClientExtStrDic = "function: 0x4e1641b0"
|  |  GetTaskTypeSpriteteName = "function: 0x4e163e10"
|  |  GetTraceInfo = "function: 0x4e163f68"
|  |  GetTraceNpcType = "function: 0x4e163de0"
|  |  GetValue = "function: 0x4e163ea8"
|  |  IsAbandon = "function: 0x4e163f08"
|  |  IsAddEscortDynamicNpc = "function: 0x4e164578"
|  |  IsMissMengTask = "function: 0x4e1620c8"
|  |  IsPassChaterFuben = "function: 0x4e163e70"
|  |  IsTaskSpecityAction = "function: 0x4e163f38"
|  |  IsTaskSpecityCategory = "function: 0x4e1640f0"
|  |  New = "function: 0x4e168a70"
|  |  NewByData = "function: 0x4e161578"
|  |  RaiseProgressIdx = "function: 0x4e164210"
|  |  RefreshTask = "function: 0x4e163ed8"
|  |  ResetEndTime = "function: 0x4e162168"
|  |  SetStatus = "function: 0x4e1645a8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4e162068"
|  }
|  m_CData = {
|  |  ChapterFb = "1,7"
|  |  autoDoNextTask = 10025
|  |  clientExtStr = ""
|  |  name = "协助调查"
|  |  submitNpcId = 11438
|  |  submitRewardStr = {
|  |  |  [1] = "R1385"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,7"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 11437
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 312
|  |  |  |  }
|  |  |  |  name = "伊露"
|  |  |  |  npcid = 33565
|  |  |  |  npctype = 11437
|  |  |  |  pos_info = {
|  |  |  |  |  x = 29600
|  |  |  |  |  y = 12700
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33566
|  |  |  |  npctype = 11438
|  |  |  |  pos_info = {
|  |  |  |  |  x = 6000
|  |  |  |  |  y = 24700
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "抓住到处乱窜的嘟噜噜。"
|  |  isdone = 0
|  |  name = "协助调查"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 11438
|  |  target = 11437
|  |  targetdesc = "跟上伊露"
|  |  taskid = 10385
|  |  taskitem = {}
|  |  tasktype = 9
|  |  time = 0
|  |  traceinfo = {
|  |  |  mapid = 101000
|  |  |  npctype = 11437
|  |  |  pos_x = 5600
|  |  |  pos_y = 23700
|  |  }
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
|  m_TraceInfo = {
|  |  mapid = 101000
|  |  npctype = 11437
|  |  pos_x = 5600
|  |  pos_y = 23700
|  }
}
[core/global.lua:59]:<color=#ffeb04> Trigger............  33565</color>
[core/table.lua:94]:Table = {
|  map_id = 101000
|  model_info = {
|  |  scale = 1
|  |  shape = 312
|  }
|  name = "伊露"
|  npcid = 33565
|  npctype = 11437
|  pos_info = {
|  |  x = 29.6
|  |  y = 12.7
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8001
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickNpc = {
|  npcid = 33565
}
[logic/model/CModel.lua:299]:3009没有这个动作idleCity
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8001
}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcSay = {
|  name = "伊露"
|  npcid = 33565
|  shape = 312
|  text = "你……你给我跪到明天，不许吃饭！"
}
[core/global.lua:59]:<color=#ffeb04> fight  false</color>
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 7005
}
[core/table.lua:94]:<--Net Send: task.C2GSAcceptTask = {
|  taskid = 10385
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 7005
}
[core/table.lua:94]:-->Net Receive: task.GS2CDialog = {
|  dialog = {
|  |  [1] = {
|  |  |  content = "不管怎样，你是最后接触嘟嘟噜的人，必须回去统帅部进行记录协助调查。"
|  |  |  next = "2"
|  |  |  pre_id_list = "11437,0"
|  |  |  status = 2
|  |  |  subid = 1
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [2] = {
|  |  |  content = "明白，我会跟你回去的。"
|  |  |  finish_event = "_STARTESCORT"
|  |  |  next = "0"
|  |  |  pre_id_list = "11437,0"
|  |  |  status = 2
|  |  |  subid = 2
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  }
|  dialog_id = 10385
|  npc_id = 33565
|  npc_name = "伊露"
|  sessionidx = "491041"
|  shape = 312
|  task_big_type = 2
|  task_small_type = 9
}
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshTask = {
|  accepttime = 1754035176
|  name = "协助调查"
|  statusinfo = {
|  |  note = "Accept Task"
|  |  status = 1
|  }
|  target = 11438
|  taskid = 10385
}
[core/table.lua:94]:Table = {
|  mapid = 101000
|  npctype = 11437
|  pos_x = 5600
|  pos_y = 23700
}
[core/table.lua:94]:<--Net Send: task.C2GSSyncTraceInfo = {
|  cur_mapid = 101000
|  cur_posx = 29600
|  cur_posy = 12700
|  taskid = 10385
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035178
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:59:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035188
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:59:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035198
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 15:59:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: huodong.GS2CHeroBoxRecord = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeScore = {
|  cur_id = 1
|  end_time = 1752505139
|  start_time = 1751209200
}
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "天降宝物，世界各地出现了大量的灵魂宝箱，欢迎冒险者们前去寻宝"
|  grade = 28
|  horse_race = 1
}
[core/global.lua:59]:<color=#ffeb04>OnContinue 3</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 3003
}
[core/table.lua:94]:<--Net Send: other.C2GSCallback = {
|  sessionidx = "491041"
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 3003
}
[core/table.lua:94]:-->Net Receive: task.GS2CStartEscort = {
|  taskid = 10385
}
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4e164120"
|  |  AssociatedPick = "function: 0x4e164180"
|  |  AssociatedSubmit = "function: 0x4e164150"
|  |  CreateDefalutData = "function: 0x4e162100"
|  |  GetChaptetFubenData = "function: 0x4e163e40"
|  |  GetProgressThing = "function: 0x4e1641e0"
|  |  GetRemainTime = "function: 0x4e162098"
|  |  GetStatus = "function: 0x4e164240"
|  |  GetTaskClientExtStrDic = "function: 0x4e1641b0"
|  |  GetTaskTypeSpriteteName = "function: 0x4e163e10"
|  |  GetTraceInfo = "function: 0x4e163f68"
|  |  GetTraceNpcType = "function: 0x4e163de0"
|  |  GetValue = "function: 0x4e163ea8"
|  |  IsAbandon = "function: 0x4e163f08"
|  |  IsAddEscortDynamicNpc = "function: 0x4e164578"
|  |  IsMissMengTask = "function: 0x4e1620c8"
|  |  IsPassChaterFuben = "function: 0x4e163e70"
|  |  IsTaskSpecityAction = "function: 0x4e163f38"
|  |  IsTaskSpecityCategory = "function: 0x4e1640f0"
|  |  New = "function: 0x4e168a70"
|  |  NewByData = "function: 0x4e161578"
|  |  RaiseProgressIdx = "function: 0x4e164210"
|  |  RefreshTask = "function: 0x4e163ed8"
|  |  ResetEndTime = "function: 0x4e162168"
|  |  SetStatus = "function: 0x4e1645a8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4e162068"
|  }
|  m_CData = {
|  |  ChapterFb = "1,7"
|  |  autoDoNextTask = 10025
|  |  clientExtStr = ""
|  |  name = "协助调查"
|  |  submitNpcId = 11438
|  |  submitRewardStr = {
|  |  |  [1] = "R1385"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,7"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 11437
|  |  accepttime = 1754035176
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 312
|  |  |  |  }
|  |  |  |  name = "伊露"
|  |  |  |  npcid = 33565
|  |  |  |  npctype = 11437
|  |  |  |  pos_info = {
|  |  |  |  |  x = 29600
|  |  |  |  |  y = 12700
|  |  |  |  }
|  |  |  }
|  |  |  [2] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33566
|  |  |  |  npctype = 11438
|  |  |  |  pos_info = {
|  |  |  |  |  x = 6000
|  |  |  |  |  y = 24700
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "抓住到处乱窜的嘟噜噜。"
|  |  isdone = 0
|  |  name = "协助调查"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "Accept Task"
|  |  |  status = 1
|  |  }
|  |  submitnpc = 11438
|  |  target = 11438
|  |  targetdesc = "跟上伊露"
|  |  taskid = 10385
|  |  taskitem = {}
|  |  tasktype = 9
|  |  time = 0
|  |  traceinfo = {
|  |  |  mapid = 101000
|  |  |  npctype = 11437
|  |  |  pos_x = 5600
|  |  |  pos_y = 23700
|  |  }
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
|  m_TraceInfo = {
|  |  cur_mapid = 101000
|  |  cur_posx = 29.6
|  |  cur_posy = 12.7
|  |  mapid = 101000
|  |  npctype = 11437
|  |  pos_x = 5600
|  |  pos_y = 23700
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/global.lua:59]:<color=#ffeb04> 开始移动 5.6 23.7</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035208
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:00:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/model/CModel.lua:299]:3009没有这个动作idleCity
[core/table.lua:94]:<--Net Send: task.C2GSSyncTraceInfo = {
|  cur_mapid = 101000
|  cur_posx = 28200
|  cur_posy = 17000
|  taskid = 10385
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点2(中)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30759
|  |  npctype = 2
|  |  owner = "领主: "
|  }
|  eid = 33
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 33
|  pos_info = {
|  |  x = 5000
|  |  y = 10000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点2(中)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 18
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1009
|  |  |  }
|  |  |  name = "神父"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 8
|  |  npctype = 5042
|  }
|  eid = 3
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 3
|  pos_info = {
|  |  x = 12200
|  |  y = 25000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1009
|  }
|  name = "神父"
|  trapmine = {}
}
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [5] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1502
|  |  |  }
|  |  |  name = "荆鸣"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 205
|  |  npctype = 5012
|  }
|  eid = 20
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 20
|  pos_info = {
|  |  x = 7000
|  |  y = 11300
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1502
|  }
|  name = "荆鸣"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 5
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 38
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 39
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 24
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 306
|  |  |  }
|  |  |  name = "袁雀"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 60
|  |  npctype = 5001
|  }
|  eid = 9
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 9
|  pos_info = {
|  |  x = 6600
|  |  y = 26200
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 306
|  }
|  name = "袁雀"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点16(中)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30787
|  |  npctype = 16
|  |  owner = "领主: "
|  }
|  eid = 26
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 26
|  pos_info = {
|  |  x = 45000
|  |  y = 25000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点16(中)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 34
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1010
|  |  |  }
|  |  |  name = "丽丝"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 69
|  |  npctype = 5004
|  }
|  eid = 12
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 12
|  pos_info = {
|  |  x = 45600
|  |  y = 26900
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1010
|  }
|  name = "丽丝"
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 4
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "1ee"
|  |  |  model_info = {
|  |  |  |  scale = 1
|  |  |  |  shape = 3009
|  |  |  }
|  |  |  name = "据点11(中)"
|  |  |  owner = "领主: "
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 30777
|  |  npctype = 11
|  |  owner = "领主: "
|  }
|  eid = 42
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 42
|  pos_info = {
|  |  x = 29000
|  |  y = 25000
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  scale = 1
|  |  shape = 3009
|  }
|  name = "据点11(中)"
|  owner = "领主: "
|  trapmine = {}
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiBlock = {
|  aoi_npc = {
|  |  block = {
|  |  |  mask = "10e"
|  |  |  model_info = {
|  |  |  |  color = {
|  |  |  |  |  [1] = 0
|  |  |  |  |  [2] = 0
|  |  |  |  |  [3] = 0
|  |  |  |  |  [4] = 0
|  |  |  |  }
|  |  |  |  scale = 1
|  |  |  |  shape = 1501
|  |  |  }
|  |  |  name = "扳尾"
|  |  |  trapmine = {}
|  |  }
|  |  npcid = 63
|  |  npctype = 5002
|  }
|  eid = 10
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:-->Net Receive: scene.GS2CEnterAoiPos = {
|  eid = 10
|  pos_info = {
|  |  x = 6000
|  |  y = 21200
|  }
|  scene_id = 12
|  type = 2
}
[core/table.lua:94]:CNetCtrl解析mask: NpcAoiBlock = {
|  model_info = {
|  |  color = {
|  |  |  [1] = 0
|  |  |  [2] = 0
|  |  |  [3] = 0
|  |  |  [4] = 0
|  |  }
|  |  scale = 1
|  |  shape = 1501
|  }
|  name = "扳尾"
|  trapmine = {}
}
[core/table.lua:94]:<--Net Send: task.C2GSSyncTraceInfo = {
|  cur_mapid = 101000
|  cur_posx = 23300
|  cur_posy = 17700
|  taskid = 10385
}
[logic/model/CModel.lua:299]:3009没有这个动作idleCity
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10122</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小叶 1509 [19.3,20.4,0] -58 false</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小佳 1510 [18.3,20,0] -22.7 false</color>
[core/table.lua:94]:<--Net Send: task.C2GSSyncTraceInfo = {
|  cur_mapid = 101000
|  cur_posx = 18400
|  cur_posy = 18100
|  taskid = 10385
}
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 之前忙着做兼职，果然这次考试成绩不好。</color>
[logic/model/CModel.lua:299]:3009没有这个动作idleCity
[core/table.lua:94]:<--Net Send: task.C2GSSyncTraceInfo = {
|  cur_mapid = 101000
|  cur_posx = 14300
|  cur_posy = 20900
|  taskid = 10385
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 2
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 21
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 25
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 26
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 12
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 13
|  scene_id = 12
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 哇！你还是一如既往的厉害，年级前10耶！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/table.lua:94]:<--Net Send: task.C2GSSyncTraceInfo = {
|  cur_mapid = 101000
|  cur_posx = 9800
|  cur_posy = 23400
|  taskid = 10385
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 19
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 6
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 23
|  scene_id = 12
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035218
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:00:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 mihu true</color>
[core/table.lua:94]:<--Net Send: task.C2GSSyncTraceInfo = {
|  cur_mapid = 101000
|  cur_posx = 5600
|  cur_posy = 23700
|  taskid = 10385
}
[core/table.lua:94]:<--Net Send: task.C2GSSyncTraceInfo = {
|  cur_mapid = 101000
|  cur_posx = 5600
|  cur_posy = 23700
|  taskid = 10385
}
[core/global.lua:59]:<color=#ffeb04> 提交任务</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 7001
}
[core/table.lua:94]:<--Net Send: task.C2GSClickTask = {
|  taskid = 10385
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 7001
}
[core/table.lua:94]:-->Net Receive: task.GS2CFindTaskPath = {
|  taskid = 10385
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 1
|  scene_id = 12
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 3003
}
[core/table.lua:94]:<--Net Send: other.C2GSCallback = {
|  sessionidx = "492041"
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 22
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 40
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: scene.GS2CLeaveAoi = {
|  eid = 15
|  scene_id = 12
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 3003
}
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/table.lua:94]:-->Net Receive: task.GS2CDialog = {
|  dialog = {
|  |  [1] = {
|  |  |  content = "好的谢谢你的协助，目前我们已经调查完毕。"
|  |  |  finish_event = "DONE"
|  |  |  next = "0"
|  |  |  pre_id_list = "11438"
|  |  |  status = 1
|  |  |  subid = 3
|  |  |  type = 3
|  |  |  ui_mode = 2
|  |  }
|  }
|  dialog_id = 10385
|  npc_id = 33566
|  npc_name = "白"
|  sessionidx = "493041"
|  shape = 801
|  task_big_type = 2
|  task_small_type = 9
}
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 是吗？我还没有找到我排名…</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  1 -155</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 你看错了，那是倒数排名。</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  2 37</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不对啊我还看到班长名字了。</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035228
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:00:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 wuyu2 true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 听说，班长在考试前被没收了漫画。</color>
[core/global.lua:59]:<color=#ffeb04>OnContinue 3</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 3003
}
[core/table.lua:94]:<--Net Send: other.C2GSCallback = {
|  sessionidx = "493041"
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 3003
}
[core/table.lua:94]:-->Net Receive: task.GS2CDelTask = {
|  done = 1
|  taskid = 10385
}
[core/table.lua:94]:Table = {
|  cur_mapid = 101000
|  cur_posx = 5.6
|  cur_posy = 23.7
|  mapid = 101000
|  npctype = 11437
|  pos_x = 5600
|  pos_y = 23700
}
[core/table.lua:94]:-->Net Receive: task.GS2CAddTask = {
|  taskdata = {
|  |  acceptnpc = 10608
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33623
|  |  |  |  npctype = 10608
|  |  |  |  pos_info = {
|  |  |  |  |  x = 6000
|  |  |  |  |  y = 24700
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "再次被拦下，这次是执行官白！"
|  |  name = "意外"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10608
|  |  target = 10608
|  |  targetdesc = "似曾相识"
|  |  taskid = 10025
|  |  taskitem = {}
|  |  tasktype = 1
|  |  traceinfo = {}
|  |  type = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>add task </color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4e164120"
|  |  AssociatedPick = "function: 0x4e164180"
|  |  AssociatedSubmit = "function: 0x4e164150"
|  |  CreateDefalutData = "function: 0x4e162100"
|  |  GetChaptetFubenData = "function: 0x4e163e40"
|  |  GetProgressThing = "function: 0x4e1641e0"
|  |  GetRemainTime = "function: 0x4e162098"
|  |  GetStatus = "function: 0x4e164240"
|  |  GetTaskClientExtStrDic = "function: 0x4e1641b0"
|  |  GetTaskTypeSpriteteName = "function: 0x4e163e10"
|  |  GetTraceInfo = "function: 0x4e163f68"
|  |  GetTraceNpcType = "function: 0x4e163de0"
|  |  GetValue = "function: 0x4e163ea8"
|  |  IsAbandon = "function: 0x4e163f08"
|  |  IsAddEscortDynamicNpc = "function: 0x4e164578"
|  |  IsMissMengTask = "function: 0x4e1620c8"
|  |  IsPassChaterFuben = "function: 0x4e163e70"
|  |  IsTaskSpecityAction = "function: 0x4e163f38"
|  |  IsTaskSpecityCategory = "function: 0x4e1640f0"
|  |  New = "function: 0x4e168a70"
|  |  NewByData = "function: 0x4e161578"
|  |  RaiseProgressIdx = "function: 0x4e164210"
|  |  RefreshTask = "function: 0x4e163ed8"
|  |  ResetEndTime = "function: 0x4e162168"
|  |  SetStatus = "function: 0x4e1645a8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4e162068"
|  }
|  m_CData = {
|  |  ChapterFb = "1,7"
|  |  autoDoNextTask = 0
|  |  clientExtStr = ""
|  |  name = "意外"
|  |  submitNpcId = 10608
|  |  submitRewardStr = {
|  |  |  [1] = "R1025"
|  |  }
|  |  taskWalkingTips = "幸好执行官们通情达理。,绝不能让人知道我的目的。;none;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10608
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33623
|  |  |  |  npctype = 10608
|  |  |  |  pos_info = {
|  |  |  |  |  x = 6000
|  |  |  |  |  y = 24700
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "再次被拦下，这次是执行官白！"
|  |  isdone = 0
|  |  name = "意外"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10608
|  |  target = 10608
|  |  targetdesc = "似曾相识"
|  |  taskid = 10025
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  abnormal_attr_ratio = 560
|  |  attack = 328
|  |  coin = 813750
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 43
|  |  energy = 246
|  |  exp = 21960
|  |  grade = 14
|  |  hp = 3091
|  |  kp_sdk_info = {
|  |  |  create_time = 1751861123
|  |  |  upgrade_time = 1754035235
|  |  }
|  |  mask = "10020000287ff62"
|  |  max_hp = 3173
|  |  power = 991
|  |  res_abnormal_ratio = 560
|  |  res_critical_ratio = 500
|  |  skill_point = 13
|  |  speed = 753
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  abnormal_attr_ratio = 560
|  attack = 328
|  coin = 813750
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 43
|  energy = 246
|  exp = 21960
|  grade = 14
|  hp = 3091
|  kp_sdk_info = {
|  |  create_time = 1751861123
|  |  upgrade_time = 1754035235
|  }
|  max_hp = 3173
|  power = 991
|  res_abnormal_ratio = 560
|  res_critical_ratio = 500
|  skill_point = 13
|  speed = 753
}
[logic/platform/CSdkCtrl.lua:393]:sdk upload: not init
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Org"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/attr/CAttrCtrl"]:143: in function 'UpdateAttr'
	[string "net/netplayer"]:9: in function <[string "net/netplayer"]:5>
	[C]: in function 'xxpcall'
	[string "net/CNetCtrl"]:318: in function 'ProtoCall'
	[string "net/CNetCtrl"]:300: in function 'ProcessProto'
	[string "net/CNetCtrl"]:251: in function 'ProtoHandlerCheck'
	[string "net/CNetCtrl"]:262: in function 'Receive'
	[string "net/CNetCtrl"]:175: in function 'OnSocketEvent'
	[string "net/CNetCtrl"]:111: in function 'm_Callback'
	[string "net/CTcpClient"]:43: in function <[string "net/CTcpClient"]:37>
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 16000
|  |  mask = "80"
|  }
|  partnerid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 16000
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得#exp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#exp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 11180
|  |  mask = "80"
|  }
|  partnerid = 2
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 11180
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#tl#G3#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#tl#G3#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#w1#G2500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G2500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B重华#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B重华#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B马面面#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B马面面#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B蛇姬#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B蛇姬#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  abnormal_attr_ratio = 400
|  |  attack = 371
|  |  critical_damage = 15000
|  |  critical_ratio = 600
|  |  cure_critical_ratio = 500
|  |  defense = 86
|  |  exp = 8600
|  |  grade = 9
|  |  hp = 3198
|  |  mask = "fffc0"
|  |  max_hp = 3198
|  |  power = 1020
|  |  res_abnormal_ratio = 500
|  |  res_critical_ratio = 600
|  |  speed = 735
|  }
|  partnerid = 3
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  abnormal_attr_ratio = 400
|  attack = 371
|  critical_damage = 15000
|  critical_ratio = 600
|  cure_critical_ratio = 500
|  defense = 86
|  exp = 8600
|  grade = 9
|  hp = 3198
|  max_hp = 3198
|  power = 1020
|  res_abnormal_ratio = 500
|  res_critical_ratio = 600
|  speed = 735
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B阿坊#n获得#hexp#G500#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B阿坊#n获得#hexp#G500#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  abnormal_attr_ratio = 400
|  |  attack = 340
|  |  critical_damage = 15000
|  |  critical_ratio = 1000
|  |  cure_critical_ratio = 500
|  |  defense = 62
|  |  exp = 3600
|  |  grade = 5
|  |  hp = 2288
|  |  mask = "fffc0"
|  |  max_hp = 2288
|  |  power = 816
|  |  res_abnormal_ratio = 500
|  |  res_critical_ratio = 400
|  |  speed = 70
|  }
|  partnerid = 5
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  abnormal_attr_ratio = 400
|  attack = 340
|  critical_damage = 15000
|  critical_ratio = 1000
|  cure_critical_ratio = 500
|  defense = 62
|  exp = 3600
|  grade = 5
|  hp = 2288
|  max_hp = 2288
|  power = 816
|  res_abnormal_ratio = 500
|  res_critical_ratio = 400
|  speed = 70
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 403
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 403
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 403
|  |  |  |  value = 9
|  |  |  }
|  |  }
|  |  id = 5
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 501
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 501
|  |  |  |  value = 1
|  |  |  }
|  |  |  [5] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 502
|  |  |  |  value = 10
|  |  |  }
|  |  |  [6] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 501
|  |  |  |  value = 5
|  |  |  }
|  |  }
|  |  id = 1
|  }
}
[core/global.lua:59]:<color=#ffeb04>找到新的引导类型 Open_Org</color>
[logic/ui/CViewCtrl.lua:94]:CGuideView ShowView
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Org"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/task/CTaskCtrl"]:2434: in function 'StartAutoDoingShiMen'
	[string "logic/guide/CGuideCtrl"]:2383: in function 'StopActionWhenGuide'
	[string "logic/guide/CGuideView"]:31: in function 'OnCreateView'
	[string "logic/ui/CViewBase"]:112: in function <[string "logic/ui/CViewBase"]:104>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:259: in function 'DoCloneCallback'
	[string "logic/base/CResCtrl"]:246: in function 'CloneWithAsset'
	[string "logic/base/CResCtrl"]:225: in function <[string "logic/base/CResCtrl"]:224>
	[C]: in function 'xxpcall'
	[string "logic/base/CResCtrl"]:266: in function 'DoAssetCallback'
	[string "logic/base/CResCtrl"]:311: in function <[string "logic/base/CResCtrl"]:305>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
[logic/ui/CViewBase.lua:125]:CGuideView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04>执行指引 Open_Org_1 1</color>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Org"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewBase"]:168: in function <[string "logic/ui/CViewBase"]:164>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Org"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/ui/CViewCtrl"]:131: in function <[string "logic/ui/CViewCtrl"]:130>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
[core/global.lua:59]:<color=#ffeb04>开始引导检查</color>
[core/table.lua:94]:Table = {
|  grade = {
|  |  [1] = "Open_Org"
|  }
}
[core/global.lua:47]:调用堆栈:
    stack traceback:
	[string "core/global"]:47: in function 'printtrace'
	[string "logic/guide/CGuideCtrl"]:227: in function 'StartCheck'
	[string "logic/guide/CGuideCtrl"]:80: in function 'TriggerCheck'
	[string "logic/guide/CGuideCtrl"]:33: in function 'TriggerAll'
	[string "logic/mainmenu/CMainMenuExpandBox"]:280: in function <[string "logic/mainmenu/CMainMenuExpandBox"]:251>
	[C]: in function 'xxpcall'
	[string "logic/base/CTimerCtrl"]:51: in function 'UpdateList'
	[string "logic/base/CTimerCtrl"]:70: in function 'Update'
	[string "main"]:34: in function <[string "main"]:31>
[core/table.lua:94]:Table = {
|  [1] = "16"
|  [2] = "20"
}
[core/global.lua:59]:<color=#ffeb04>14 16 20</color>
[core/table.lua:94]:Table = {
|  [1] = "21"
|  [2] = "24"
}
[core/global.lua:59]:<color=#ffeb04>14 21 24</color>
[core/table.lua:94]:Table = {
|  [1] = "25"
|  [2] = "27"
}
[core/global.lua:59]:<color=#ffeb04>14 25 27</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  2 zhenjing true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035238
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:00:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>引导结束 Open_Org Open_Org_1 1</color>
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Org_1"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1001201
|  }
}
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Tips_Org_0"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 5000700
|  }
}
[logic/ui/CViewCtrl.lua:94]:CGuideMaskView ShowView
[logic/ui/CViewCtrl.lua:104]:CGuideView     CloseView
[logic/guide/CGuideCtrl.lua:333]:已完成引导Open_Org
[logic/guide/CGuideCtrl.lua:2358]:C2GSFinishGuidance>>>>>>>>>>>>>
[core/table.lua:94]:Table = {
|  [1] = "N1Guide_Open_Org"
}
[core/table.lua:94]:<--Net Send: teach.C2GSFinishGuidance = {
|  key = {
|  |  [1] = 1012099
|  }
}
[core/global.lua:59]:<color=#ffeb04>停止引导检查,并且下一帧再检测引导</color>
[logic/ui/CViewBase.lua:125]:CGuideMaskView LoadDone!
[logic/ui/CViewCtrl.lua:104]:CGuideMaskView     CloseView
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 那她一定因为伤心过度所以考砸了。</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerFaceTo  2 43</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 说起来考试前我看了一本小说，推荐哦！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好看吗，借我啦！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035248
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:00:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  2 [12.3,25.5,0] 90</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [11.6,25,0] 90</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  2 不要啦，人家还没看完呢！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 别小气嘛！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  2 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035258
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:00:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035268
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:01:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4e164120"
|  |  AssociatedPick = "function: 0x4e164180"
|  |  AssociatedSubmit = "function: 0x4e164150"
|  |  CreateDefalutData = "function: 0x4e162100"
|  |  GetChaptetFubenData = "function: 0x4e163e40"
|  |  GetProgressThing = "function: 0x4e1641e0"
|  |  GetRemainTime = "function: 0x4e162098"
|  |  GetStatus = "function: 0x4e164240"
|  |  GetTaskClientExtStrDic = "function: 0x4e1641b0"
|  |  GetTaskTypeSpriteteName = "function: 0x4e163e10"
|  |  GetTraceInfo = "function: 0x4e163f68"
|  |  GetTraceNpcType = "function: 0x4e163de0"
|  |  GetValue = "function: 0x4e163ea8"
|  |  IsAbandon = "function: 0x4e163f08"
|  |  IsAddEscortDynamicNpc = "function: 0x4e164578"
|  |  IsMissMengTask = "function: 0x4e1620c8"
|  |  IsPassChaterFuben = "function: 0x4e163e70"
|  |  IsTaskSpecityAction = "function: 0x4e163f38"
|  |  IsTaskSpecityCategory = "function: 0x4e1640f0"
|  |  New = "function: 0x4e168a70"
|  |  NewByData = "function: 0x4e161578"
|  |  RaiseProgressIdx = "function: 0x4e164210"
|  |  RefreshTask = "function: 0x4e163ed8"
|  |  ResetEndTime = "function: 0x4e162168"
|  |  SetStatus = "function: 0x4e1645a8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4e162068"
|  }
|  m_CData = {
|  |  ChapterFb = "1,7"
|  |  autoDoNextTask = 0
|  |  clientExtStr = ""
|  |  name = "意外"
|  |  submitNpcId = 10608
|  |  submitRewardStr = {
|  |  |  [1] = "R1025"
|  |  }
|  |  taskWalkingTips = "幸好执行官们通情达理。,绝不能让人知道我的目的。;none;none"
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,7"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10608
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33623
|  |  |  |  npctype = 10608
|  |  |  |  pos_info = {
|  |  |  |  |  x = 6000
|  |  |  |  |  y = 24700
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "再次被拦下，这次是执行官白！"
|  |  isdone = 0
|  |  name = "意外"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10608
|  |  target = 10608
|  |  targetdesc = "似曾相识"
|  |  taskid = 10025
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[logic/model/CHero.lua:65]:same sync pos
[core/global.lua:59]:<color=#ffeb04> Trigger............  33623</color>
[core/table.lua:94]:Table = {
|  map_id = 101000
|  model_info = {
|  |  scale = 1
|  |  shape = 801
|  }
|  name = "白"
|  npcid = 33623
|  npctype = 10608
|  pos_info = {
|  |  x = 6
|  |  y = 24.7
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 8001
}
[core/table.lua:94]:<--Net Send: npc.C2GSClickNpc = {
|  npcid = 33623
}
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 8001
}
[core/table.lua:94]:-->Net Receive: npc.GS2CNpcSay = {
|  name = "白"
|  npcid = 33623
|  shape = 801
|  text = "尔从何处来，就归何处去，魑魅魍魉速速散开！"
}
[core/global.lua:59]:<color=#ffeb04> fight  false</color>
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 7005
}
[core/table.lua:94]:<--Net Send: task.C2GSAcceptTask = {
|  taskid = 10025
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 7005
}
[core/table.lua:94]:-->Net Receive: task.GS2CDialog = {
|  dialog = {
|  |  [1] = {
|  |  |  content = "之前是我们误会了，请你原谅。"
|  |  |  next = "2"
|  |  |  pre_id_list = "10608,0"
|  |  |  status = 2
|  |  |  subid = 1
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [2] = {
|  |  |  content = "小意思，那我走了。"
|  |  |  next = "3"
|  |  |  pre_id_list = "10608,0"
|  |  |  status = 2
|  |  |  subid = 2
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [3] = {
|  |  |  content = "嗯……请稍等！你有点脸熟，我们过去是不是在哪里见过？"
|  |  |  next = "4"
|  |  |  pre_id_list = "10608,0"
|  |  |  status = 2
|  |  |  subid = 3
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  |  [4] = {
|  |  |  content = "看来城里人都比较容易认错人？我还是第一次来帝都，过去从来没有见过你。"
|  |  |  next = "5"
|  |  |  pre_id_list = "10608,0"
|  |  |  status = 2
|  |  |  subid = 4
|  |  |  type = 4
|  |  |  ui_mode = 2
|  |  }
|  |  [5] = {
|  |  |  content = "不好意思，请让我再次确认一下。"
|  |  |  finish_event = "DONE"
|  |  |  next = "0"
|  |  |  pre_id_list = "10608,0"
|  |  |  status = 2
|  |  |  subid = 5
|  |  |  type = 5
|  |  |  ui_mode = 2
|  |  }
|  }
|  dialog_id = 10025
|  npc_id = 33623
|  npc_name = "白"
|  sessionidx = "494041"
|  shape = 801
|  task_big_type = 2
|  task_small_type = 1
}
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:-->Net Receive: task.GS2CRefreshTask = {
|  accepttime = 1754035269
|  name = "意外"
|  statusinfo = {
|  |  note = "Accept Task"
|  |  status = 1
|  }
|  target = 10608
|  taskid = 10025
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035278
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:01:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035288
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:01:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035298
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:01:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035308
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:01:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10122</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035318
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:01:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035328
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:02:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 1</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035338
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:02:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04>OnContinue 3</color>
[core/table.lua:94]:<--Net Send: other.C2GSClientSession = {
|  session = 3003
}
[core/table.lua:94]:<--Net Send: other.C2GSCallback = {
|  sessionidx = "494041"
}
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/table.lua:94]:-->Net Receive: other.GS2CSessionResponse = {
|  session = 3003
}
[core/table.lua:94]:-->Net Receive: task.GS2CDelTask = {
|  done = 1
|  taskid = 10025
}
[core/table.lua:94]:-->Net Receive: task.GS2CAddTask = {
|  taskdata = {
|  |  acceptnpc = 10609
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33624
|  |  |  |  npctype = 10609
|  |  |  |  pos_info = {
|  |  |  |  |  x = 8000
|  |  |  |  |  y = 23300
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "告别白大人，前往帝都武馆答谢荆鸣吧！"
|  |  name = "不相识"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10609
|  |  target = 10609
|  |  targetdesc = "白的误会"
|  |  taskid = 10026
|  |  taskitem = {}
|  |  tasktype = 1
|  |  traceinfo = {}
|  |  type = 2
|  }
}
[core/global.lua:59]:<color=#ffeb04>add task </color>
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4e164120"
|  |  AssociatedPick = "function: 0x4e164180"
|  |  AssociatedSubmit = "function: 0x4e164150"
|  |  CreateDefalutData = "function: 0x4e162100"
|  |  GetChaptetFubenData = "function: 0x4e163e40"
|  |  GetProgressThing = "function: 0x4e1641e0"
|  |  GetRemainTime = "function: 0x4e162098"
|  |  GetStatus = "function: 0x4e164240"
|  |  GetTaskClientExtStrDic = "function: 0x4e1641b0"
|  |  GetTaskTypeSpriteteName = "function: 0x4e163e10"
|  |  GetTraceInfo = "function: 0x4e163f68"
|  |  GetTraceNpcType = "function: 0x4e163de0"
|  |  GetValue = "function: 0x4e163ea8"
|  |  IsAbandon = "function: 0x4e163f08"
|  |  IsAddEscortDynamicNpc = "function: 0x4e164578"
|  |  IsMissMengTask = "function: 0x4e1620c8"
|  |  IsPassChaterFuben = "function: 0x4e163e70"
|  |  IsTaskSpecityAction = "function: 0x4e163f38"
|  |  IsTaskSpecityCategory = "function: 0x4e1640f0"
|  |  New = "function: 0x4e168a70"
|  |  NewByData = "function: 0x4e161578"
|  |  RaiseProgressIdx = "function: 0x4e164210"
|  |  RefreshTask = "function: 0x4e163ed8"
|  |  ResetEndTime = "function: 0x4e162168"
|  |  SetStatus = "function: 0x4e1645a8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4e162068"
|  }
|  m_CData = {
|  |  ChapterFb = "1,8"
|  |  autoDoNextTask = 10027
|  |  clientExtStr = ""
|  |  name = "不相识"
|  |  submitNpcId = 10609
|  |  submitRewardStr = {
|  |  |  [1] = "R1026"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10609
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33624
|  |  |  |  npctype = 10609
|  |  |  |  pos_info = {
|  |  |  |  |  x = 8000
|  |  |  |  |  y = 23300
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "告别白大人，前往帝都武馆答谢荆鸣吧！"
|  |  isdone = 0
|  |  name = "不相识"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10609
|  |  target = 10609
|  |  targetdesc = "白的误会"
|  |  taskid = 10026
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[core/table.lua:94]:-->Net Receive: player.GS2CPropChange = {
|  role = {
|  |  coin = 816320
|  |  exp = 22510
|  |  mask = "60"
|  }
}
[core/table.lua:94]:CNetCtrl解析mask: role = {
|  coin = 816320
|  exp = 22510
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  abnormal_attr_ratio = 400
|  |  attack = 503
|  |  critical_damage = 15000
|  |  critical_ratio = 800
|  |  cure_critical_ratio = 500
|  |  defense = 106
|  |  exp = 16550
|  |  grade = 14
|  |  hp = 3684
|  |  mask = "fffc0"
|  |  max_hp = 3684
|  |  power = 1271
|  |  res_abnormal_ratio = 500
|  |  res_critical_ratio = 600
|  |  speed = 420
|  }
|  partnerid = 1
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  abnormal_attr_ratio = 400
|  attack = 503
|  critical_damage = 15000
|  critical_ratio = 800
|  cure_critical_ratio = 500
|  defense = 106
|  exp = 16550
|  grade = 14
|  hp = 3684
|  max_hp = 3684
|  power = 1271
|  res_abnormal_ratio = 500
|  res_critical_ratio = 600
|  speed = 420
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  abnormal_attr_ratio = 400
|  |  attack = 393
|  |  critical_damage = 15000
|  |  critical_ratio = 800
|  |  cure_critical_ratio = 500
|  |  defense = 92
|  |  exp = 11730
|  |  grade = 11
|  |  hp = 3157
|  |  mask = "fffc0"
|  |  max_hp = 3157
|  |  power = 1048
|  |  res_abnormal_ratio = 500
|  |  res_critical_ratio = 500
|  |  speed = 315
|  }
|  partnerid = 2
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  abnormal_attr_ratio = 400
|  attack = 393
|  critical_damage = 15000
|  critical_ratio = 800
|  cure_critical_ratio = 500
|  defense = 92
|  exp = 11730
|  grade = 11
|  hp = 3157
|  max_hp = 3157
|  power = 1048
|  res_abnormal_ratio = 500
|  res_critical_ratio = 500
|  speed = 315
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 9150
|  |  mask = "80"
|  }
|  partnerid = 3
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 9150
}
[core/table.lua:94]:-->Net Receive: partner.GS2CPartnerPropChange = {
|  partner_info = {
|  |  exp = 4150
|  |  mask = "80"
|  }
|  partnerid = 5
}
[core/table.lua:94]:CNetCtrl解析mask: partner = {
|  exp = 4150
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得#exp#G550#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得#exp#G550#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "获得了#w1#G2570#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "获得了#w1#G2570#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B重华#n获得#hexp#G550#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B重华#n获得#hexp#G550#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B马面面#n获得#hexp#G550#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B马面面#n获得#hexp#G550#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B蛇姬#n获得#hexp#G550#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B蛇姬#n获得#hexp#G550#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: notify.GS2CNotify = {
|  cmd = "#B阿坊#n获得#hexp#G550#n"
}
[core/table.lua:94]:-->Net Receive: chat.GS2CConsumeMsg = {
|  content = "#B阿坊#n获得#hexp#G550#n"
|  type = 6
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 302
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 302
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 302
|  |  |  |  value = 14
|  |  |  }
|  |  }
|  |  id = 4
|  }
}
[core/table.lua:94]:-->Net Receive: achieve.GS2CPictureDegree = {
|  info = {
|  |  cur = {
|  |  |  [1] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [2] = {
|  |  |  |  key = "伙伴星级"
|  |  |  |  targetid = 501
|  |  |  |  value = 1
|  |  |  }
|  |  |  [3] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 502
|  |  |  |  value = 1
|  |  |  }
|  |  |  [4] = {
|  |  |  |  key = "伙伴数量"
|  |  |  |  targetid = 501
|  |  |  |  value = 1
|  |  |  }
|  |  |  [5] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 502
|  |  |  |  value = 11
|  |  |  }
|  |  |  [6] = {
|  |  |  |  key = "伙伴等级"
|  |  |  |  targetid = 501
|  |  |  |  value = 5
|  |  |  }
|  |  }
|  |  id = 1
|  }
}
[logic/map/CMapCtrl.lua:1134]:<color=#00FF00> >>> .AddTaskChapterNpcNpc | 实例一个跟踪Npc | Table:AddTraceNpc </color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035348
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:02:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035358
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:02:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035368
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:02:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:Table = {
|  classtype = {
|  |  AssociatedNpc = "function: 0x4e164120"
|  |  AssociatedPick = "function: 0x4e164180"
|  |  AssociatedSubmit = "function: 0x4e164150"
|  |  CreateDefalutData = "function: 0x4e162100"
|  |  GetChaptetFubenData = "function: 0x4e163e40"
|  |  GetProgressThing = "function: 0x4e1641e0"
|  |  GetRemainTime = "function: 0x4e162098"
|  |  GetStatus = "function: 0x4e164240"
|  |  GetTaskClientExtStrDic = "function: 0x4e1641b0"
|  |  GetTaskTypeSpriteteName = "function: 0x4e163e10"
|  |  GetTraceInfo = "function: 0x4e163f68"
|  |  GetTraceNpcType = "function: 0x4e163de0"
|  |  GetValue = "function: 0x4e163ea8"
|  |  IsAbandon = "function: 0x4e163f08"
|  |  IsAddEscortDynamicNpc = "function: 0x4e164578"
|  |  IsMissMengTask = "function: 0x4e1620c8"
|  |  IsPassChaterFuben = "function: 0x4e163e70"
|  |  IsTaskSpecityAction = "function: 0x4e163f38"
|  |  IsTaskSpecityCategory = "function: 0x4e1640f0"
|  |  New = "function: 0x4e168a70"
|  |  NewByData = "function: 0x4e161578"
|  |  RaiseProgressIdx = "function: 0x4e164210"
|  |  RefreshTask = "function: 0x4e163ed8"
|  |  ResetEndTime = "function: 0x4e162168"
|  |  SetStatus = "function: 0x4e1645a8"
|  |  classname = "CTask"
|  |  ctor = "function: 0x4e162068"
|  }
|  m_CData = {
|  |  ChapterFb = "1,8"
|  |  autoDoNextTask = 10027
|  |  clientExtStr = ""
|  |  name = "不相识"
|  |  submitNpcId = 10609
|  |  submitRewardStr = {
|  |  |  [1] = "R1026"
|  |  }
|  |  taskWalkingTips = ""
|  |  tips = 0
|  |  type = 2
|  }
|  m_ChapterFb = "1,8"
|  m_Finish = false
|  m_SData = {
|  |  acceptgrade = 0
|  |  acceptnpc = 10609
|  |  autotype = 1
|  |  clientnpc = {
|  |  |  [1] = {
|  |  |  |  map_id = 101000
|  |  |  |  model_info = {
|  |  |  |  |  scale = 1
|  |  |  |  |  shape = 801
|  |  |  |  }
|  |  |  |  name = "白"
|  |  |  |  npcid = 33624
|  |  |  |  npctype = 10609
|  |  |  |  pos_info = {
|  |  |  |  |  x = 8000
|  |  |  |  |  y = 23300
|  |  |  |  }
|  |  |  }
|  |  }
|  |  detaildesc = "告别白大人，前往帝都武馆答谢荆鸣吧！"
|  |  isdone = 0
|  |  name = "不相识"
|  |  patrolinfo = {}
|  |  placeinfo = {}
|  |  playid = 0
|  |  rewardinfo = 0
|  |  shapeinfo = {}
|  |  statusinfo = {
|  |  |  note = "CreateTask"
|  |  |  status = 2
|  |  }
|  |  submitnpc = 10609
|  |  target = 10609
|  |  targetdesc = "白的误会"
|  |  taskid = 10026
|  |  taskitem = {}
|  |  tasktype = 1
|  |  time = 0
|  |  traceinfo = {}
|  |  type = 2
|  }
|  m_TaskType = {
|  |  appenddes = "单人"
|  |  dropable = 0
|  |  icon = "pic_zhuxian_tubiao"
|  |  id = 2
|  |  menu_index = 2
|  |  menu_show_index = 1
|  |  menu_show_index_sort = 1
|  |  name = "主线"
|  }
}
[logic/ui/CViewCtrl.lua:94]:CDialogueMainView ShowView
[logic/ui/CViewBase.lua:125]:CDialogueMainView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035378
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:02:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035388
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:03:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035398
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:03:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035408
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:03:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035418
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:03:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035428
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:03:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035438
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:03:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035448
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:04:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035458
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:04:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035468
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:04:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035478
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:04:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035488
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:04:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035498
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:04:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035508
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:05:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:94]:CLockScreenView ShowView
[logic/ui/CViewBase.lua:125]:CLockScreenView LoadDone!
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035518
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:05:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035528
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:05:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035538
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:05:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035548
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:05:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[logic/ui/CViewCtrl.lua:104]:CDialogueMainView     CloseView
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035558
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:05:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035568
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:06:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035578
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:06:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035588
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:06:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035598
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:06:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035608
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:06:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035618
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:06:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035628
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:07:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035638
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:07:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035648
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:07:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035658
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:07:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035668
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:07:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035678
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:07:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035688
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:08:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035698
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:08:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035708
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:08:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035718
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:08:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035728
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:08:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035738
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:08:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035748
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:09:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035758
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:09:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035768
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:09:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035778
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:09:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035788
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:09:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035798
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:09:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035808
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:10:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035818
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:10:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035828
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:10:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035838
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:10:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035848
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:10:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035858
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:10:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035868
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:11:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035878
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:11:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035888
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:11:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035898
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:11:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035908
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:11:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035918
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:11:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035928
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:12:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035938
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:12:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035948
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:12:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035958
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:12:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035968
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:12:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035978
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:12:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035988
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:13:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754035998
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:13:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036008
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:13:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036018
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:13:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036028
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:13:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036038
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:13:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036048
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:14:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036058
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:14:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036068
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:14:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036078
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:14:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036088
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:14:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036098
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:14:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036108
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:15:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036118
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:15:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036128
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:15:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036138
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:15:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036148
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:15:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036158
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:15:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036168
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:16:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036178
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:16:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036188
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:16:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036198
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:16:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036208
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:16:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036218
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:16:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036228
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:17:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036238
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:17:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036248
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:17:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036258
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:17:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036268
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:17:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036278
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:17:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036288
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:18:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036298
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:18:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036308
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:18:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036318
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:18:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036328
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:18:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036338
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:18:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036348
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:19:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036358
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:19:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036368
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:19:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036378
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:19:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036388
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:19:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036398
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:19:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036408
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:20:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036418
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:20:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036428
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:20:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036438
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:20:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036448
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:20:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036458
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:20:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036468
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:21:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036478
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:21:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036488
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:21:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036498
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:21:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036508
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:21:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036518
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:21:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036528
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:22:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036538
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:22:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036548
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:22:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036558
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:22:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036568
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:22:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036578
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:22:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036588
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:23:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036598
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:23:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036608
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:23:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036618
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:23:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036628
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:23:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036638
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:23:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036648
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:24:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036658
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:24:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036668
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:24:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036678
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:24:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036688
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:24:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036698
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:24:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036708
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:25:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036718
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:25:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036728
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:25:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036738
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:25:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036748
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:25:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036758
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:25:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036768
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:26:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036778
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:26:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036788
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:26:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036798
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:26:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036808
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:26:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036818
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:26:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036828
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:27:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036838
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:27:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036848
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:27:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036858
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:27:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036868
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:27:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036878
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:27:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036888
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:28:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036898
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:28:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036908
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:28:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036918
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:28:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036928
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:28:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036938
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:28:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036948
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:29:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036958
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:29:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036968
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:29:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036978
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:29:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036988
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:29:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754036998
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:29:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037008
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:30:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037018
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:30:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037028
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:30:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037038
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:30:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037048
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:30:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037058
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:30:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037068
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:31:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037078
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:31:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037088
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:31:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037098
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:31:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037108
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:31:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037118
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:31:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037128
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:32:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037138
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:32:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037148
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:32:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037158
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:32:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037168
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:32:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037178
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:32:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037188
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:33:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037198
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:33:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037208
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:33:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037218
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:33:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037228
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:33:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037238
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:33:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037248
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:34:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037258
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:34:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037268
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:34:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037278
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:34:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037288
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:34:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037298
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:34:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037308
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:35:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037318
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:35:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037328
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:35:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037338
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:35:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037348
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:35:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037358
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:35:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037368
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:36:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037378
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:36:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037388
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:36:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037398
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:36:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037408
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:36:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037418
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:36:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037428
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:37:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037438
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:37:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037448
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:37:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037458
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:37:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037468
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:37:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037478
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:37:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037488
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:38:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037498
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:38:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037508
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:38:28
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037518
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:38:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037528
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:38:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037538
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:38:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037548
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:39:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037558
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:39:18
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037569
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:39:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037578
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:39:38
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037588
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:39:48
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037598
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:39:58
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037608
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:40:08
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037619
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:40:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037629
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:40:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037639
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:40:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037649
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:40:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037659
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:40:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037669
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:41:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037679
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:41:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037689
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:41:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037699
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:41:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037709
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:41:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037719
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:41:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037729
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:42:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037739
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:42:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037749
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:42:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037759
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:42:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037769
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:42:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037779
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:42:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037789
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:43:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037799
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:43:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037809
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:43:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037819
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:43:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037829
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:43:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037839
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:43:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037849
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:44:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037859
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:44:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037869
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:44:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037879
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:44:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037889
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:44:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037899
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:44:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037909
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:45:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037919
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:45:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037929
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:45:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037939
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:45:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037949
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:45:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037959
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:45:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037969
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:46:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037979
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:46:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037989
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:46:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754037999
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:46:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038009
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:46:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038019
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:46:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038029
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:47:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038039
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:47:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038049
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:47:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038059
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:47:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038069
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:47:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038079
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:47:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038089
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:48:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038099
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:48:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038109
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:48:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038119
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:48:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038129
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:48:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038139
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:48:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038149
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:49:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038159
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:49:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038169
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:49:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038179
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:49:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038189
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:49:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038199
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:49:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038209
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:50:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038219
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:50:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038229
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:50:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038239
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:50:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038249
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:50:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038259
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:50:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038269
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:51:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038279
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:51:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038289
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:51:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038299
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:51:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038309
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:51:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038319
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:51:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038329
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:52:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038339
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:52:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038349
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:52:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038359
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:52:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038369
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:52:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038379
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:52:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038389
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:53:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038399
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:53:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038409
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:53:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038419
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:53:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038429
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:53:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038439
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:53:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038449
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:54:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038459
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:54:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038469
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:54:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038479
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:54:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038489
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:54:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038499
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:54:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038509
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:55:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038519
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:55:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038529
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:55:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038539
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:55:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038549
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:55:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038559
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:55:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038569
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:56:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038579
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:56:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038589
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:56:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038599
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:56:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038609
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:56:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038619
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:56:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038629
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:57:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038639
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:57:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038649
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:57:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038659
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:57:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038669
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:57:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038679
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:57:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038689
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:58:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038699
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:58:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038709
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:58:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038719
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:58:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038729
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:58:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038739
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:58:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038749
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:59:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038759
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:59:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038769
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:59:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038779
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:59:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038789
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:59:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038799
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 16:59:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: huodong.GS2CHeroBoxRecord = {}
[core/table.lua:94]:-->Net Receive: huodong.GS2CChargeScore = {
|  cur_id = 1
|  end_time = 1752505139
|  start_time = 1751209200
}
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "天降宝物，世界各地出现了大量的灵魂宝箱，欢迎冒险者们前去寻宝"
|  grade = 28
|  horse_race = 1
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038809
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:00:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038819
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:00:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038829
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:00:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038839
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:00:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038849
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:00:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038859
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:00:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038869
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:01:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038879
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:01:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038889
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:01:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:-->Net Receive: chat.GS2CSysChat = {
|  content = "亲们，我们的工作人员绝不会在游戏中索要您的任何信息，切记不要将自己的信息告诉陌生人，以免造成您的损失"
}
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038899
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:01:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038909
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:01:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038919
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:01:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038929
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:02:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038939
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:02:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10116</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10101
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10101</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  神父 1009 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 今天的风儿甚是喧嚣！主上保佑终于借到想要的书了！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [4.2,19.6,0] 360</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 上次坏掉的手表应该修好了吧？</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038949
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:02:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038959
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:02:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038969
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:02:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038979
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:02:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038989
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:03:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754038999
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:03:19
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754039009
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:03:29
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> 剧本结束 10101</color>
[core/table.lua:94]:Table = {
|  [1] = {
|  |  id = 10107
|  |  triggerNpc = 5047
|  }
|  [2] = {
|  |  id = 10102
|  |  triggerNpc = 5043
|  }
|  [3] = {
|  |  id = 10103
|  |  triggerNpc = 5044
|  }
|  [4] = {
|  |  id = 10115
|  |  triggerNpc = 5041
|  }
|  [5] = {
|  |  id = 10122
|  |  triggerNpc = 5064
|  }
|  [6] = {
|  |  id = 10116
|  |  triggerNpc = 5042
|  }
}
[core/global.lua:59]:<color=#ffeb04> 指令生成完毕 10116</color>
[core/global.lua:59]:<color=#ffeb04> 剧本开始</color>
[core/global.lua:59]:<color=#ffeb04> AddPlayer  小美 1508 [12.2,25,0] -165 false</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 终于借到这套漫画了，开心！</color>
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [25.9,21.2,0] 150</color>
[core/global.lua:59]:<color=#ffeb04> PlayerShowSocialEmoji  1 kaixin true</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754039019
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:03:39
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerDoAction  1 show</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754039029
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:03:49
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [18.9,20.1,0] -44.7</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 好像忘记了什么重要的事情？</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 啊！下星期就要期末考了！</color>
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754039039
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:03:59
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[core/global.lua:59]:<color=#ffeb04> PlayerRunto  1 [12.2,25.2,0] 34.5</color>
[core/global.lua:59]:<color=#ffeb04> PlayerSay  1 我还没有开始复习！</color>
[core/global.lua:59]:<color=#ffeb04> SetPlayerActive  1 false</color>
[logic/ui/CViewCtrl.lua:104]:CLockScreenView     CloseView
[logic/ui/CViewCtrl.lua:94]:CLimitRewardView ShowView
[core/table.lua:94]:<--Net Send: fuli.C2GSGetLuckDrawInfo = {}
[logic/ui/CViewBase.lua:125]:CLimitRewardView LoadDone!
[core/table.lua:94]:-->Net Receive: fuli.GS2CLuckDrawUI = {
|  cnt = 3
|  cost = 100
|  idxlist = {
|  |  [1] = 1
|  |  [2] = 2
|  |  [3] = 3
|  |  [4] = 4
|  |  [5] = 5
|  |  [6] = 6
|  |  [7] = 7
|  |  [8] = 8
|  }
}
break了
break了
[core/table.lua:94]:<--Net Send: other.C2GSHeartBeat = {}
[core/table.lua:94]:-->Net Receive: other.GS2CHeartBeat = {
|  time = 1754039049
}
[logic/misc/CTimeCtrl.lua:80]:同步服务器时间    2025/08/01 17:04:09
[logic/misc/CTimeCtrl.lua:38]:HeartBeat->心跳包检测正常
[logic/ui/CViewCtrl.lua:104]:CLimitRewardView     CloseView
