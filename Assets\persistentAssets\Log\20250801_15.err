谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000046C2812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000046C28014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000046CEA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004DF9BFF3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004DF9C105 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000046CEABE3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x0000000046CEA634 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x0000000046CEA867 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000046C2812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000046C28014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000046CEA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004DF9BFF3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004DF9BE4F (Mono JIT Code) [GoogleEventHandle.cs:198] GoogleEventHandle:GooglePayInit () 
0x000000004DF9B904 (Mono JIT Code) [ShareSDKManagerWrap.cs:105] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x000000004DD9B813 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E14F1F7 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x000000004DF7B35C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004DF7B1DA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004DF8B0B7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004DF8AF00 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000004DF8A0EC (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x000000004DF89D7E (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000A674F2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000046C2812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000046C28014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000046CEA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004DF9BFF3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004DF9BE4F (Mono JIT Code) [GoogleEventHandle.cs:198] GoogleEventHandle:GooglePayInit () 
0x000000004DF9B904 (Mono JIT Code) [ShareSDKManagerWrap.cs:105] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x000000004DD9B813 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E14F1F7 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x000000004DF7B35C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004DF7B1DA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004DF8B0B7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004DF8AF00 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000004DF8A0EC (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x000000004DF89D7E (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000A674F2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:124: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000046C2812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000046C28014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000046CEA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004DF9BFF3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000004DF9C105 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000046CEABE3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x0000000046CEA634 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x0000000046CEA867 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000046C2812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000046C28014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000046CEA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004DF9BFF3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x0000000071F574A0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x0000000071F57237 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x000000004A439453 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E0F6B49 ((<unknown>)) 
0x00007FF80E0F6C19 ((<unknown>)) 
0x00007FF80E0F6D8F ((<unknown>)) 
0x00007FF80E0FA41B ((<unknown>)) 
0x00007FF80E0F29C7 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x000000004DF7B35C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004DF7B1DA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004DF8B0B7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004DF8AF00 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006CDD3820 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000006CDD3656 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000006CDD3580 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000006CDD34B1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000A674F2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000006CDC54F1 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000006CDC5230 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x000000006CDCE649 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000006CDC64D2 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x0000000046CE91BD (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x0000000046CAD42A (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x0000000046CABA61 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x0000000046CAB8F6 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000A674F2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:124: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000046C2812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000046C28014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000046CEA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000004DF9BFF3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x0000000071F574A0 (Mono JIT Code) [GameDebug.cs:166] GameDebug:LogLuaError (string,string) 
0x0000000071F57237 (Mono JIT Code) [UtilsWrap.cs:481] CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr) 
0x000000004A439453 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogLuaError (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E0F6B49 ((<unknown>)) 
0x00007FF80E0F6C19 ((<unknown>)) 
0x00007FF80E0F6D8F ((<unknown>)) 
0x00007FF80E0FA41B ((<unknown>)) 
0x00007FF80E0F29C7 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x000000004DF7B35C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000004DF7B1DA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000004DF8B0B7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000004DF8AF00 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006CDD3820 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000006CDD3656 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000006CDD3580 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000006CDD34B1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000A674F2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000006CDC54F1 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000006CDC5230 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x000000006CDCE649 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000006CDC64D2 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x0000000046CE91BD (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x0000000046CAD42A (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x0000000046CABA61 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x0000000046CAB8F6 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000A674F2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


[string "logic/ui/UITools"]:167: attempt to index local 'oTarget' (a nil value)
stack traceback:
	[string "core/global"]:81: in function '__index'
	[string "logic/ui/UITools"]:167: in function 'MoveToTarget'
	[string "logic/limitreward/CTotalPayPage"]:124: in function 'func'
	[string "logic/ui/CUIEventHandler"]:109: in function <[string "logic/ui/CUIEventHandler"]:99>
	[C]: in function 'xxpcall'
	[string "logic/base/CDelegate"]:20: in function <[string "logic/base/CDelegate"]:17>

谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000046C2814B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000046C28034 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000046CEA4F5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000005E39B663 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000005E39B775 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000046CEAC03 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x0000000046CEA654 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x0000000046CEA887 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000046C2814B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000046C28034 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000046CEA4F5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000005E39B663 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000005E39B4BF (Mono JIT Code) [GoogleEventHandle.cs:198] GoogleEventHandle:GooglePayInit () 
0x000000005E39AF74 (Mono JIT Code) [ShareSDKManagerWrap.cs:105] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x000000005E19A9F3 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E14F1F7 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x000000005E37A75C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000005E37A5DA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000005E38A4B7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000005E38A300 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000005E3894EC (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x000000005E38917E (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000A674F2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000046C2814B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000046C28034 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000046CEA4F5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000005E39B663 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000005E39B4BF (Mono JIT Code) [GoogleEventHandle.cs:198] GoogleEventHandle:GooglePayInit () 
0x000000005E39AF74 (Mono JIT Code) [ShareSDKManagerWrap.cs:105] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x000000005E19A9F3 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E14F1F7 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x000000005E37A75C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000005E37A5DA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000005E38A4B7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000005E38A300 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000005E3894EC (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x000000005E38917E (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000A674F2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


break了
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000046C2814B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000046C28034 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000046CEA4F5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000005E39B663 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000005E39B775 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000046CEAC03 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x0000000046CEA654 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x0000000046CEA887 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000046C2814B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000046C28034 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000046CEA4F5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000005E39B663 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000005E39B775 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000071749AD1 (Mono JIT Code) [UtilsWrap.cs:465] CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr) 
0x000000004A438B23 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x000000005E37A75C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000005E37A5DA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000005E38A4B7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000005E38A300 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006C377760 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000006C377596 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000006C3774C0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000006C3773F1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000A674F2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000006C35C4D1 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000006C35C210 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x000000006C374B39 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000006C35D4B2 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x0000000046CE91DD (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x0000000046CAD44A (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x0000000046CABA81 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x0000000046CAB916 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000A674F2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


break了
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x0000000046C2814B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000046C28034 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x0000000046CEA4F5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x000000005E39B663 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x000000005E39B775 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000071749AD1 (Mono JIT Code) [UtilsWrap.cs:465] CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr) 
0x000000004A438B23 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x000000005E37A75C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x000000005E37A5DA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x000000005E38A4B7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x000000005E38A300 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006C377760 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000006C377596 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000006C3774C0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000006C3773F1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000A674F2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000006C35C4D1 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000006C35C210 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x000000006C374B39 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000006C35D4B2 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x0000000046CE91DD (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x0000000046CAD44A (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x0000000046CABA81 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x0000000046CAB916 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000A674F2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


break了
   at GameDebug.LogError(System.String msg, System.String stackTrace)
   at CLASSTAG_UtilsWrap.FUNCTAG_LogError(IntPtr L)
   at LuaInterface.LuaDLL.lua_pcall(IntPtr , Int32 , Int32 , Int32 )
   at LuaInterface.LuaStatePtr.LuaPCall(Int32 nArgs, Int32 nResults, Int32 errfunc)
   at LuaInterface.LuaState.PCall(Int32 args, Int32 oldTop)
   at LuaInterface.LuaFunction.PCall()
   at GlobalEventHanlder.Call(Int32 id, Int32 int1)
   at UIEventHandler.Call(EventType type)
   at UIEventHandler.<AddEventType>m__0(UnityEngine.GameObject go)
   at UIEventHandler.OnClick()
   at UnityEngine.GameObject.SendMessage(System.String , System.Object , SendMessageOptions )
   at UICamera.Notify(UnityEngine.GameObject go, System.String funcName, System.Object obj)
   at UICamera.ProcessRelease(Boolean isMouse, Single drag)
   at UICamera.ProcessTouch(Boolean pressed, Boolean released)
   at UICamera.ProcessMouse()
   at UICamera.ProcessTouches()
   at UICamera.ProcessEvents()
   at UICamera.Update()

谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F815B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8044 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA9D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470EBAD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470EBBE5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000432BB0E3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000432BAB34 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000432BAD67 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F815B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8044 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA9D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470EBAD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470EB92F (Mono JIT Code) [GoogleEventHandle.cs:198] GoogleEventHandle:GooglePayInit () 
0x00000000470EB3E4 (Mono JIT Code) [ShareSDKManagerWrap.cs:105] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x0000000046C1AE73 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E14F1F7 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470CABDC (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470CAA5A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470DA927 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470DA770 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x00000000470D995C (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x00000000470D95EE (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F815B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8044 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA9D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470EBAD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470EB92F (Mono JIT Code) [GoogleEventHandle.cs:198] GoogleEventHandle:GooglePayInit () 
0x00000000470EB3E4 (Mono JIT Code) [ShareSDKManagerWrap.cs:105] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x0000000046C1AE73 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E14F1F7 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470CABDC (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470CAA5A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470DA927 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470DA770 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x00000000470D995C (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x00000000470D95EE (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


break了
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F815B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8044 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA9D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470EBAD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470EBBE5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000432BB0E3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000432BAB34 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000432BAD67 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F815B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8044 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA9D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470EBAD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470EBBE5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000074964921 (Mono JIT Code) [UtilsWrap.cs:465] CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr) 
0x0000000044798B23 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470CABDC (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470CAA5A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470DA927 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470DA770 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000007246A540 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000007246A376 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000007246A2A0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000007246A1D1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000006BF90621 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000006BF90360 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x0000000072468E49 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000006BF91602 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000432B96BD (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004327D92A (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004327BF61 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004327BDF6 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


break了
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F815B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8044 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA9D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470EBAD3 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470EBBE5 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000074964921 (Mono JIT Code) [UtilsWrap.cs:465] CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr) 
0x0000000044798B23 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470CABDC (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470CAA5A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470DA927 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470DA770 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000007246A540 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000007246A376 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000007246A2A0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000007246A1D1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000006BF90621 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000006BF90360 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x0000000072468E49 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000006BF91602 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000432B96BD (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004327D92A (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004327BF61 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004327BDF6 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


break了
   at GameDebug.LogError(System.String msg, System.String stackTrace)
   at CLASSTAG_UtilsWrap.FUNCTAG_LogError(IntPtr L)
   at LuaInterface.LuaDLL.lua_pcall(IntPtr , Int32 , Int32 , Int32 )
   at LuaInterface.LuaStatePtr.LuaPCall(Int32 nArgs, Int32 nResults, Int32 errfunc)
   at LuaInterface.LuaState.PCall(Int32 args, Int32 oldTop)
   at LuaInterface.LuaFunction.PCall()
   at GlobalEventHanlder.Call(Int32 id, Int32 int1)
   at UIEventHandler.Call(EventType type)
   at UIEventHandler.<AddEventType>m__0(UnityEngine.GameObject go)
   at UIEventHandler.OnClick()
   at UnityEngine.GameObject.SendMessage(System.String , System.Object , SendMessageOptions )
   at UICamera.Notify(UnityEngine.GameObject go, System.String funcName, System.Object obj)
   at UICamera.ProcessRelease(Boolean isMouse, Single drag)
   at UICamera.ProcessTouch(Boolean pressed, Boolean released)
   at UICamera.ProcessMouse()
   at UICamera.ProcessTouches()
   at UICamera.ProcessEvents()
   at UICamera.Update()

谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004320812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000043208014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432CA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470FB653 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470FB765 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000432CABE3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000432CA634 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000432CA867 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004320812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000043208014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432CA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470FB653 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470FB4AF (Mono JIT Code) [GoogleEventHandle.cs:198] GoogleEventHandle:GooglePayInit () 
0x00000000470FAF64 (Mono JIT Code) [ShareSDKManagerWrap.cs:105] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x0000000046C1A9F3 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E14F1F7 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470DA75C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470DA5DA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470EA4A7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470EA2F0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x00000000470E94DC (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x00000000470E916E (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004320812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000043208014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432CA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470FB653 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470FB4AF (Mono JIT Code) [GoogleEventHandle.cs:198] GoogleEventHandle:GooglePayInit () 
0x00000000470FAF64 (Mono JIT Code) [ShareSDKManagerWrap.cs:105] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x0000000046C1A9F3 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E14F1F7 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470DA75C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470DA5DA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470EA4A7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470EA2F0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x00000000470E94DC (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x00000000470E916E (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


break了
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004320812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000043208014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432CA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470FB653 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470FB765 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000432CABE3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000432CA634 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000432CA867 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004320812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000043208014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432CA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470FB653 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470FB765 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000072182011 (Mono JIT Code) [UtilsWrap.cs:465] CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr) 
0x0000000044798B23 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470DA75C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470DA5DA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470EA4A7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470EA2F0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006BF97310 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000006BF97146 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000006BF97070 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000006BF96FA1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000006BF7C491 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000006BF7C1D0 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x000000006BF946E9 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000006BF7D472 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000432C91BD (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004328D42A (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004328BA61 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004328B8F6 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


break了
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000000004320812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x0000000043208014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432CA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470FB653 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470FB765 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000072182011 (Mono JIT Code) [UtilsWrap.cs:465] CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr) 
0x0000000044798B23 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470DA75C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470DA5DA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470EA4A7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470EA2F0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x000000006BF97310 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x000000006BF97146 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x000000006BF97070 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x000000006BF96FA1 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x000000006BF7C491 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x000000006BF7C1D0 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x000000006BF946E9 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x000000006BF7D472 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000432C91BD (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004328D42A (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004328BA61 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004328B8F6 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


break了
   at GameDebug.LogError(System.String msg, System.String stackTrace)
   at CLASSTAG_UtilsWrap.FUNCTAG_LogError(IntPtr L)
   at LuaInterface.LuaDLL.lua_pcall(IntPtr , Int32 , Int32 , Int32 )
   at LuaInterface.LuaStatePtr.LuaPCall(Int32 nArgs, Int32 nResults, Int32 errfunc)
   at LuaInterface.LuaState.PCall(Int32 args, Int32 oldTop)
   at LuaInterface.LuaFunction.PCall()
   at GlobalEventHanlder.Call(Int32 id, Int32 int1)
   at UIEventHandler.Call(EventType type)
   at UIEventHandler.<AddEventType>m__0(UnityEngine.GameObject go)
   at UIEventHandler.OnClick()
   at UnityEngine.GameObject.SendMessage(System.String , System.Object , SendMessageOptions )
   at UICamera.Notify(UnityEngine.GameObject go, System.String funcName, System.Object obj)
   at UICamera.ProcessRelease(Boolean isMouse, Single drag)
   at UICamera.ProcessTouch(Boolean pressed, Boolean released)
   at UICamera.ProcessMouse()
   at UICamera.ProcessTouches()
   at UICamera.ProcessEvents()
   at UICamera.Update()

谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F814B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8034 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA4E5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470FB273 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470FB385 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000432BABF3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000432BA644 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000432BA877 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F814B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8034 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA4E5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470FB273 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470FB0CF (Mono JIT Code) [GoogleEventHandle.cs:198] GoogleEventHandle:GooglePayInit () 
0x00000000470FAB84 (Mono JIT Code) [ShareSDKManagerWrap.cs:105] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x0000000046C1A873 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E14F1F7 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470DA5DC (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470DA45A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470EA337 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470EA180 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x00000000470E936C (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x00000000470E8FFE (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F814B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8034 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA4E5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470FB273 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470FB0CF (Mono JIT Code) [GoogleEventHandle.cs:198] GoogleEventHandle:GooglePayInit () 
0x00000000470FAB84 (Mono JIT Code) [ShareSDKManagerWrap.cs:105] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x0000000046C1A873 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E14F1F7 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470DA5DC (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470DA45A (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470EA337 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470EA180 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x00000000470E936C (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x00000000470E8FFE (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470EBC53 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470EBD65 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000432BABE3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000432BA634 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000432BA867 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470EBC53 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470EBAAF (Mono JIT Code) [GoogleEventHandle.cs:198] GoogleEventHandle:GooglePayInit () 
0x00000000470EB564 (Mono JIT Code) [ShareSDKManagerWrap.cs:105] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x0000000046C1B813 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E14F1F7 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470CAD5C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470CABDA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470DAAA7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470DA8F0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x00000000470D9ADC (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x00000000470D976E (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


谷歌支付初始化失败
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470EBC53 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470EBAAF (Mono JIT Code) [GoogleEventHandle.cs:198] GoogleEventHandle:GooglePayInit () 
0x00000000470EB564 (Mono JIT Code) [ShareSDKManagerWrap.cs:105] CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr) 
0x0000000046C1B813 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_ShareSDKManagerWrap:FUNCTAG_Google_Pay_Init (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E14F1F7 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470CAD5C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470CABDA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470DAAA7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470DA8F0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x00000000470D9ADC (Mono JIT Code) [LuaFunction.cs:120] LuaInterface.LuaFunction:Call () 
0x00000000470D976E (Mono JIT Code) [LuaMain.cs:92] LuaMain:Start () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F9308 (Unity) MonoBehaviour::Start
0x00000001409F944F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507546 (Unity) BaseBehaviourManager::CommonUpdate<LateBehaviourManager>
0x0000000140507618 (Unity) LateBehaviourManager::Update
0x0000000140725143 (Unity) `InitPlayerLoopCallbacks'::`41'::PreLateUpdateScriptRunBehaviourLateUpdateRegistrator::Forward
0x000000014072225E (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411F81A7 (Unity) PlayerLoopController::EnterPlayMode
0x00000001411F8A66 (Unity) PlayerLoopController::SetIsPlaying
0x00000001411F98B6 (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


break了
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470EBC53 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470EBD65 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000432BABE3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000432BA634 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000432BA867 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470EBC53 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470EBD65 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000071732171 (Mono JIT Code) [UtilsWrap.cs:465] CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr) 
0x0000000044798FF3 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470CAD5C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470CABDA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470DAAA7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470DA8F0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x0000000063FD7690 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x0000000063FD74C6 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x0000000063FD73F0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x0000000063FD7321 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x0000000063FBC801 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x0000000063FBC540 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x0000000063FD4A69 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x0000000063FBD7E2 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000432B91BD (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004327D42A (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004327BA61 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004327B8F6 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


break了
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470EBC53 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470EBD65 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000071732171 (Mono JIT Code) [UtilsWrap.cs:465] CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr) 
0x0000000044798FF3 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470CAD5C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470CABDA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470DAAA7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470DA8F0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x0000000063FD7690 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x0000000063FD74C6 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x0000000063FD73F0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x0000000063FD7321 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x0000000063FBC801 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x0000000063FBC540 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x0000000063FD4A69 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x0000000063FBD7E2 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000432B91BD (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004327D42A (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004327BA61 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004327B8F6 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


break了
   at GameDebug.LogError(System.String msg, System.String stackTrace)
   at CLASSTAG_UtilsWrap.FUNCTAG_LogError(IntPtr L)
   at LuaInterface.LuaDLL.lua_pcall(IntPtr , Int32 , Int32 , Int32 )
   at LuaInterface.LuaStatePtr.LuaPCall(Int32 nArgs, Int32 nResults, Int32 errfunc)
   at LuaInterface.LuaState.PCall(Int32 args, Int32 oldTop)
   at LuaInterface.LuaFunction.PCall()
   at GlobalEventHanlder.Call(Int32 id, Int32 int1)
   at UIEventHandler.Call(EventType type)
   at UIEventHandler.<AddEventType>m__0(UnityEngine.GameObject go)
   at UIEventHandler.OnClick()
   at UnityEngine.GameObject.SendMessage(System.String , System.Object , SendMessageOptions )
   at UICamera.Notify(UnityEngine.GameObject go, System.String funcName, System.Object obj)
   at UICamera.ProcessRelease(Boolean isMouse, Single drag)
   at UICamera.ProcessTouch(Boolean pressed, Boolean released)
   at UICamera.ProcessMouse()
   at UICamera.ProcessTouches()
   at UICamera.ProcessEvents()
   at UICamera.Update()

break了
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470EBC53 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470EBD65 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x00000000432BABE3 (Mono JIT Code) [GameDebug.cs:117] GameDebug:FUNCTAG_OnMessageReceived (string,string,UnityEngine.LogType) 
0x00000000432BA634 (Mono JIT Code) [ApplicationBindings.gen.cs:565] UnityEngine.Application:CallLogCallback (string,string,UnityEngine.LogType,bool) 
0x00000000432BA867 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object_object_int_sbyte (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x0000000140A2EB2E (Unity) ScriptingInvocation::Invoke<ScriptingObjectPtr>
0x0000000141427861 (Unity) LogCallbackImplementation
0x00000001406DE5C4 (Unity) CallbackArray3<DebugStringToFileData const & __ptr64,enum LogType,bool>::Invoke
0x00000001406DF912 (Unity) DebugStringToFilePostprocessedStacktrace
0x00000001406E00DC (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470EBC53 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470EBD65 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000071732171 (Mono JIT Code) [UtilsWrap.cs:465] CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr) 
0x0000000044798FF3 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470CAD5C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470CABDA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470DAAA7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470DA8F0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x0000000063FD7690 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x0000000063FD74C6 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x0000000063FD73F0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x0000000063FD7321 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x0000000063FBC801 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x0000000063FBC540 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x0000000063FD4A69 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x0000000063FBD7E2 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000432B91BD (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004327D42A (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004327BA61 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004327B8F6 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


break了
0x00000001413022E6 (Unity) StackWalker::GetCurrentCallstack
0x0000000141302E5F (Unity) StackWalker::ShowCallstack
0x00000001411C0C80 (Unity) GetStacktrace
0x00000001406DFE83 (Unity) DebugStringToFile
0x00000001406E0661 (Unity) DebugStringToFile
0x000000014143CC52 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000000431F812B (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,string,UnityEngine.Object)
0x00000000431F8014 (Mono JIT Code) [DebugLogHandler.cs:9] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[]) 
0x00000000432BA4D5 (Mono JIT Code) [Logger.cs:47] UnityEngine.Logger:Log (UnityEngine.LogType,object) 
0x00000000470EBC53 (Mono JIT Code) [DebugBindings.gen.cs:124] UnityEngine.Debug:LogError (object) 
0x00000000470EBD65 (Mono JIT Code) [GameDebug.cs:142] GameDebug:LogError (string,string) 
0x0000000071732171 (Mono JIT Code) [UtilsWrap.cs:465] CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr) 
0x0000000044798FF3 (Mono JIT Code) (wrapper native-to-managed) CLASSTAG_UtilsWrap:FUNCTAG_LogError (intptr)
0x00007FF80E0D2A58 ((<unknown>)) 
0x00007FF80E0F24E0 ((<unknown>)) 
0x00007FF80E107122 ((<unknown>)) 
0x00000000470CAD5C (Mono JIT Code) (wrapper managed-to-native) LuaInterface.LuaDLL:lua_pcall (intptr,int,int,int)
0x00000000470CABDA (Mono JIT Code) [LuaStatePtr.cs:311] LuaInterface.LuaStatePtr:LuaPCall (int,int,int) 
0x00000000470DAAA7 (Mono JIT Code) [LuaState.cs:650] LuaInterface.LuaState:PCall (int,int) 
0x00000000470DA8F0 (Mono JIT Code) [LuaFunction.cs:96] LuaInterface.LuaFunction:PCall () 
0x0000000063FD7690 (Mono JIT Code) [GlobalEventHanlder.cs:27] GlobalEventHanlder:Call (int,int) 
0x0000000063FD74C6 (Mono JIT Code) [UIEventHandler.cs:545] UIEventHandler:Call (UIEventHandler/EventType) 
0x0000000063FD73F0 (Mono JIT Code) [UIEventHandler.cs:222] UIEventHandler:<AddEventType>m__0 (UnityEngine.GameObject) 
0x0000000063FD7321 (Mono JIT Code) [UIEventHandler.cs:76] UIEventHandler:OnClick () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409F5F3F (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x00000001409F7AB6 (Unity) MonoBehaviour::InvokeMethodOrCoroutineChecked
0x0000000140A2F13D (Unity) Scripting::SendScriptingMessage
0x0000000140A2F1F5 (Unity) Scripting::SendScriptingMessage
0x000000014143FB39 (Unity) GameObject_CUSTOM_SendMessage
0x0000000063FBC801 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
0x0000000063FBC540 (Mono JIT Code) [UICamera.cs:1630] UICamera:Notify (UnityEngine.GameObject,string,object) 
0x0000000063FD4A69 (Mono JIT Code) [UICamera.cs:2490] UICamera:ProcessRelease (bool,single) 
0x0000000063FBD7E2 (Mono JIT Code) [UICamera.cs:2535] UICamera:ProcessTouch (bool,bool) 
0x00000000432B91BD (Mono JIT Code) [UICamera.cs:1976] UICamera:ProcessMouse () 
0x000000004327D42A (Mono JIT Code) [UICamera.cs:2090] UICamera:ProcessTouches () 
0x000000004327BA61 (Mono JIT Code) [UICamera.cs:1803] UICamera:ProcessEvents () 
0x000000004327B8F6 (Mono JIT Code) [UICamera.cs:1762] UICamera:Update () 
0x0000000000A674E2 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void__this__ (object,intptr,intptr,intptr)
0x00007FFF47C564D7 (mono) [mini.c:4937] mono_jit_runtime_invoke 
0x00007FFF47BA8A31 (mono) [object.c:2625] mono_runtime_invoke 
0x0000000140A39FAC (Unity) scripting_method_invoke
0x0000000140A2D8DA (Unity) ScriptingInvocation::Invoke
0x00000001409EF241 (Unity) MonoBehaviour::CallMethodIfAvailable
0x00000001409F946F (Unity) MonoBehaviour::CallUpdateMethod
0x0000000140507806 (Unity) BaseBehaviourManager::CommonUpdate<BehaviourManager>
0x0000000140507E88 (Unity) BehaviourManager::Update
0x0000000140724FD3 (Unity) `InitPlayerLoopCallbacks'::`38'::UpdateScriptRunBehaviourUpdateRegistrator::Forward
0x00000001407221A8 (Unity) PlayerLoop
0x00000001411EDC45 (Unity) PlayerLoopController::UpdateScene
0x00000001411EF1C8 (Unity) PlayerLoopController::UpdateSceneIfNeeded
0x00000001411F97CD (Unity) Application::TickTimer
0x0000000141420F5C (Unity) MainMessageLoop
0x000000014142281C (Unity) WinMain
0x0000000141E7BA08 (Unity) __tmainCRTStartup
0x00007FF8456CE8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84661C34C (ntdll) RtlUserThreadStart


break了
   at GameDebug.LogError(System.String msg, System.String stackTrace)
   at CLASSTAG_UtilsWrap.FUNCTAG_LogError(IntPtr L)
   at LuaInterface.LuaDLL.lua_pcall(IntPtr , Int32 , Int32 , Int32 )
   at LuaInterface.LuaStatePtr.LuaPCall(Int32 nArgs, Int32 nResults, Int32 errfunc)
   at LuaInterface.LuaState.PCall(Int32 args, Int32 oldTop)
   at LuaInterface.LuaFunction.PCall()
   at GlobalEventHanlder.Call(Int32 id, Int32 int1)
   at UIEventHandler.Call(EventType type)
   at UIEventHandler.<AddEventType>m__0(UnityEngine.GameObject go)
   at UIEventHandler.OnClick()
   at UnityEngine.GameObject.SendMessage(System.String , System.Object , SendMessageOptions )
   at UICamera.Notify(UnityEngine.GameObject go, System.String funcName, System.Object obj)
   at UICamera.ProcessRelease(Boolean isMouse, Single drag)
   at UICamera.ProcessTouch(Boolean pressed, Boolean released)
   at UICamera.ProcessMouse()
   at UICamera.ProcessTouches()
   at UICamera.ProcessEvents()
   at UICamera.Update()

